<template>
    <div style="height:100%">
        <BackTop z-index="9999" description="顶部" :right="20" type="primary" :bottom="10" :style="defaultCustomStyle" />
        <div class="app-container">
            <!-- 左侧布局区域 -->
            <div class="left-sidebar" :class="{ 'open': isLeftSidebarOpen }">
                <!-- 标签页导航 -->
                <div class="sidebar-tabs">
                    <div class="tab-item" :class="{ 'active': activeTab === 'scenarios' }" @click="activeTab = 'scenarios'">
                        场景
                    </div>
                    <div class="tab-item" :class="{ 'active': activeTab === 'history' }" @click="activeTab = 'history'">
                        历史记录
                    </div>
                </div>

                <!-- 场景功能卡片面板 -->
                <div v-if="activeTab === 'scenarios'" class="scenarios-panel">
                    <div class="scenario-item" @click="switchToMode('content_creation')">
                        <div class="scenario-icon">
                            <img src="@/assets/images/nrcz.png" alt="内容创作" class="scenario-icon-img">
                        </div>
                        <div class="scenario-text">
                            <div class="scenario-title">内容创作</div>
                            <div class="scenario-desc">文案生成，公文辅助写作，语言翻译等</div>
                        </div>
                    </div>

                    <div class="scenario-item" @click="switchToMode('policy_qa')">
                        <div class="scenario-icon">
                            <img src="@/assets/images/zcwd.png" alt="政策问答" class="scenario-icon-img">
                        </div>
                        <div class="scenario-text">
                            <div class="scenario-title">政策问答</div>
                            <div class="scenario-desc">政策问答，办事指引，法规条例询问</div>
                        </div>
                    </div>

                    <div class="scenario-item" @click="switchToMode('v2')">
                        <div class="scenario-icon">
                            <img src="@/assets/images/ncws.png" alt="暖城问数" class="scenario-icon-img">
                        </div>
                        <div class="scenario-text">
                            <div class="scenario-title">i暖城问数</div>
                            <div class="scenario-desc">实地问题分析</div>
                        </div>
                    </div>

                    <div class="scenario-item" @click="switchToMode('WORK_REPORT')">
                        <div class="scenario-icon">
                            <img src="@/assets/images/ncws.png" alt="创城工作报告" class="scenario-icon-img">
                        </div>
                        <div class="scenario-text">
                            <div class="scenario-title">创城工作报告</div>
                            <div class="scenario-desc">创城工作总结，报告生成，进度分析</div>
                        </div>
                    </div>
                </div>

                <!-- 历史记录面板 -->
                <div v-if="activeTab === 'history'" class="history-sidebar">
                    <div class="history-header">
                        <div class="d-flex align-center justify-space-between w-100">
                            <div class="d-flex align-center">
                                <v-icon color="primary" class="mr-2">mdi-history</v-icon>
                                <span class="font-weight-medium">历史记录</span>
                            </div>
                            <div class="d-flex">
                                <v-btn variant="text" density="comfortable" icon size="small" color="error"
                                    @click="!isProcessing && clearHistory()"
                                    :disabled="chatHistory.length == 0 || isProcessing"
                                    :class="{ 'unselectable-btn': isProcessing }">
                                    <v-icon>mdi-delete-sweep</v-icon>
                                </v-btn>
                            </div>
                        </div>
                    </div>

                    <div v-if="chatHistory.length == 0" class="text-center pa-4 text-body-1 text-medium-emphasis">
                        暂无历史记录
                    </div>

                    <div v-else class="history-groups">
                        <!-- 7天内的历史 -->
                        <div class="history-group" v-if="chatHistory.recent7Days && chatHistory.recent7Days.length > 0">
                            <div class="history-group-title" @click="toggleGroup('recent')">
                                <div class="d-flex align-center flex-grow-1">
                                    <v-icon size="x-small" color="grey-darken-1"
                                        class="mr-2">mdi-calendar-range</v-icon>
                                    <div style="width: 50px;">近7天内</div>
                                    <div class="history-count">({{ chatHistory.recent7Days.length }})</div>
                                </div>
                                <v-icon size="small" color="grey-darken-1" class="collapse-icon"
                                    :class="{ 'collapsed': !groupExpanded.recent }">
                                    mdi-chevron-down
                                </v-icon>
                            </div>
                            <div class="history-items" v-show="groupExpanded.recent">
                                <div v-for="(history, index) in chatHistory.recent7Days" :key="index"
                                    @click="!isProcessing && loadHistoryConversation(history)" class="history-item"
                                    :class="{
                                        'selected-history': (selectedHistoryIndex === 0 && index === 0) || selectedHistoryIndex == getHistoryFullIndex(history),
                                        'disabled-history': isProcessing
                                    }">
                                    <!-- 左侧内容区域，点击时触发加载历史记录 -->
                                    <div class="history-item-main"
                                        @click="!isProcessing && loadHistoryConversation(history)">
                                        <v-avatar size="28"
                                            :color="history.mode?.value == 'v2' ? 'green-lighten-5' : 'blue-lighten-5'"
                                            class="history-avatar">
                                            <v-icon size="small"
                                                :color="history.mode?.value == 'v2' ? 'green-darken-1' : 'blue-darken-1'">
                                                {{ history.mode?.value == 'v2' ? 'mdi-chart-bar' :
                                                    'mdi-message-text-outline' }}
                                            </v-icon>
                                        </v-avatar>
                                        <div class="history-text">
                                            <div class="history-title">{{ getHistoryTitle(history) }}</div>
                                            <div class="history-date">
                                                <v-icon size="x-small" color="grey-darken-1">mdi-clock-outline</v-icon>
                                                <span>{{ formatHistoryDate(history.lastMessageTime ||
                                                    history.createTime) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 右侧三点菜单按钮 -->
                                    <el-dropdown size="large" trigger="click" @command="handleCommand"
                                        :disabled="isProcessing">
                                        <button class="history-menu-btn"
                                            @click.stop="!isProcessing && openHistoryMenu($event, history)"
                                            :class="{ 'unselectable-btn': isProcessing }">
                                            <v-icon size="small">mdi-dots-vertical</v-icon>
                                        </button>
                                        <template #dropdown>
                                            <el-dropdown-menu>
                                                <!-- <el-dropdown-item :command="{ type: 'rename', history }">重命名</el-dropdown-item> -->
                                                <el-dropdown-item
                                                    :command="{ type: 'delete', history }">删除</el-dropdown-item>
                                            </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                </div>
                            </div>
                        </div>

                        <!-- 30天内的历史 -->
                        <div class="history-group"
                            v-if="chatHistory.recent30Days && chatHistory.recent30Days.length > 0">
                            <div class="history-group-title" @click="toggleGroup('older')">
                                <div class="d-flex align-center flex-grow-1">
                                    <v-icon size="x-small" color="grey-darken-1"
                                        class="mr-2">mdi-calendar-month</v-icon>
                                    <span>30天内</span>
                                    <span class="history-count">({{ chatHistory.recent30Days.length }})</span>
                                </div>
                                <v-icon size="small" color="grey-darken-1" class="collapse-icon"
                                    :class="{ 'collapsed': !groupExpanded.older }">
                                    mdi-chevron-down
                                </v-icon>
                            </div>
                            <div class="history-items" v-show="groupExpanded.older">
                                <div v-for="(history, index) in chatHistory.recent30Days" :key="index"
                                    @click="!isProcessing && loadHistoryConversation(history)" class="history-item"
                                    :class="{
                                        'selected-history': selectedHistoryIndex == getHistoryFullIndex(history),
                                        'disabled-history': isProcessing
                                    }">
                                    <!-- 左侧内容区域，点击时触发加载历史记录 -->
                                    <div class="history-item-main"
                                        @click="!isProcessing && loadHistoryConversation(history)">
                                        <v-avatar size="28"
                                            :color="history.mode?.value === 'v2' ? 'green-lighten-5' : 'blue-lighten-5'"
                                            class="history-avatar">
                                            <v-icon size="small"
                                                :color="history.mode?.value === 'v2' ? 'green-darken-1' : 'blue-darken-1'">
                                                {{ history.mode?.value === 'v2' ? 'mdi-chart-bar' :
                                                    'mdi-message-text-outline' }}
                                            </v-icon>
                                        </v-avatar>
                                        <div class="history-text">
                                            <div class="history-title">{{ getHistoryTitle(history) }}</div>
                                            <div class="history-date">
                                                <v-icon size="x-small" color="grey-darken-1">mdi-clock-outline</v-icon>
                                                <span>{{ formatHistoryDate(history.lastMessageTime ||
                                                    history.createTime) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 右侧三点菜单按钮 -->
                                    <el-dropdown size="large" trigger="click" @command="handleCommand">
                                        <button class="history-menu-btn" @click.stop="openHistoryMenu($event, history)">
                                            <v-icon size="small">mdi-dots-vertical</v-icon>
                                        </button>
                                        <template #dropdown>
                                            <el-dropdown-menu>
                                                <!-- <el-dropdown-item :command="{ type: 'rename', history }">重命名</el-dropdown-item> -->
                                                <el-dropdown-item
                                                    :command="{ type: 'delete', history }">删除</el-dropdown-item>
                                            </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                </div>
                            </div>
                        </div>

                        <!-- 30天前的历史 -->
                        <div class="history-group"
                            v-if="chatHistory.before30Days && chatHistory.before30Days.length > 0">
                            <div class="history-group-title" @click="toggleGroup('oldest')">
                                <div class="d-flex align-center flex-grow-1">
                                    <v-icon size="x-small" color="grey-darken-1"
                                        class="mr-2">mdi-calendar-clock</v-icon>
                                    <span>30天前</span>
                                    <span class="history-count">({{ chatHistory.before30Days.length }})</span>
                                </div>
                                <v-icon size="small" color="grey-darken-1" class="collapse-icon"
                                    :class="{ 'collapsed': !groupExpanded.oldest }">
                                    mdi-chevron-down
                                </v-icon>
                            </div>
                            <div class="history-items" v-show="groupExpanded.oldest">
                                <div v-for="(history, index) in chatHistory.before30Days" :key="index"
                                    @click="!isProcessing && loadHistoryConversation(history)" class="history-item"
                                    :class="{
                                        'selected-history': selectedHistoryIndex == getHistoryFullIndex(history),
                                        'disabled-history': isProcessing
                                    }">
                                    <!-- 左侧内容区域，点击时触发加载历史记录 -->
                                    <div class="history-item-main"
                                        @click="!isProcessing && loadHistoryConversation(history)">
                                        <v-avatar size="28"
                                            :color="history.mode?.value === 'v2' ? 'green-lighten-5' : 'blue-lighten-5'"
                                            class="history-avatar">
                                            <v-icon size="small"
                                                :color="history.mode?.value === 'v2' ? 'green-darken-1' : 'blue-darken-1'">
                                                {{ history.mode?.value === 'v2' ? 'mdi-chart-bar' :
                                                    'mdi-message-text-outline' }}
                                            </v-icon>
                                        </v-avatar>
                                        <div class="history-text">
                                            <div class="history-title">{{ getHistoryTitle(history) }}</div>
                                            <div class="history-date">
                                                <v-icon size="x-small" color="grey-darken-1">mdi-clock-outline</v-icon>
                                                <span>{{ formatHistoryDate(history.lastMessageTime ||
                                                    history.createTime) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 右侧三点菜单按钮 -->
                                    <el-dropdown size="large" trigger="click" @command="handleCommand">
                                        <button class="history-menu-btn" @click.stop="openHistoryMenu($event, history)">
                                            <v-icon size="small">mdi-dots-vertical</v-icon>
                                        </button>
                                        <template #dropdown>
                                            <el-dropdown-menu>
                                                <!-- <el-dropdown-item :command="{ type: 'rename', history }">重命名</el-dropdown-item> -->
                                                <el-dropdown-item
                                                    :command="{ type: 'delete', history }">删除</el-dropdown-item>
                                            </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="right-content">
                <!-- 移动端菜单按钮 -->
                <div class="mobile-menu-btn" @click="toggleLeftSidebar">
                    <v-icon>mdi-menu</v-icon>
                </div>

                <!-- 聊天区域 - 直接铺满整个右侧 -->
                <div id="chatSection" class="chat-container full-height"
                    :class="{ 'v2-mode': selectedMode.value == 'v2' }">
                    <div class="chat-header py-3 py-sm-4 px-4 px-sm-6 d-flex justify-space-between align-center">
                        <div class="d-flex align-center">
                            <v-icon left class="mr-2 d-none d-sm-inline">mdi-message-text</v-icon>
                            智能问答
                        </div>
                        <div class="d-flex">
                            <v-btn variant="text" class="ml-2" :icon="$vuetify.display.xs"
                                @click="!isProcessing && createNewSession(true)" :disabled="isProcessing"
                                :class="{ 'unselectable-btn': isProcessing }">
                                <v-icon>mdi-plus</v-icon>
                                <span class="d-none d-sm-inline ml-1">开启新对话</span>
                            </v-btn>
                        </div>
                    </div>

                    <div class="chat-messages" ref="chatContainer">
                        <div v-for="(msg, index) in messages" :key="index" :class="['message', msg.type]">
                            <div class="message-avatar" v-if="msg.type == 'system'">
                                <v-avatar color="primary" :size="$vuetify.display.xs ? 32 : 40">
                                    <v-icon color="white" :size="$vuetify.display.xs ? 16 : 24">mdi-robot</v-icon>
                                </v-avatar>
                            </div>
                            <div class="message-avatar" v-if="msg.type == 'user'">
                                <v-avatar color="blue-lighten-4" :size="$vuetify.display.xs ? 32 : 40">
                                    <v-icon color="blue-darken-1"
                                        :size="$vuetify.display.xs ? 16 : 24">mdi-account</v-icon>
                                </v-avatar>
                            </div>
                            <div class="message-content" :class="{ 'table-container': msg.table }">
                                <!-- 思考过程直接放到上面 -->
                                <template v-if="msg.thinking">
                                    <div v-html="msg.content"></div>
                                </template>

                                <!-- 非思考中状态显示内容 -->
                                <template v-else>
                                    <!-- 思考步骤容器已移除，思考内容现在直接在正文中以灰色背景显示 -->

                                    <!-- 正式输出内容，不包含思考过程 -->
                                    <!-- 用户消息直接显示内容 -->
                                    <div v-if="msg.type === 'user'" v-html="msg.content" class="formal-content"
                                        style="max-width: 100%; word-wrap: break-word; overflow-wrap: break-word;">
                                    </div>
                                    <!-- 系统消息直接显示当前内容，支持流式输出 -->
                                    <template v-else>
                                        <div v-html="msg.content" class="formal-content"
                                            style="max-width: 100%; word-wrap: break-word; overflow-wrap: break-word;">
                                        </div>
                                    </template>
                                </template>

                                <!-- 参考文档列表 -->
                                <div v-if="msg.type === 'system' && msg.docAggs && msg.docAggs.length > 0"
                                    class="reference-docs-container">
                                    <div class="reference-docs-header">
                                        <span>参考文档</span>
                                    </div>
                                    <div class="reference-docs-list">
                                        <div v-for="(doc, docIndex) in msg.docAggs" :key="docIndex"
                                            class="reference-doc-item" @click="viewFileInline(doc)">
                                            <v-icon size="small" color="primary"
                                                class="mr-2">mdi-file-document-outline</v-icon>
                                            <span class="reference-doc-name">{{ doc.doc_name }}</span>
                                        </div>
                                    </div>
                                </div>


                                <!-- 建议问题 - 只在i暖城问数模式下显示 -->
                                <div v-if="msg.welcome && msg.suggestedQuestions && msg.suggestedQuestions.length > 0 && selectedMode.value === 'v2'"
                                    class="suggested-questions">
                                    <div v-for="(question, qIndex) in msg.suggestedQuestions" :key="qIndex"
                                        class="suggested-question" @click="askSuggestedQuestion(question)">
                                        {{ question }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 返回顶部按钮 -->
                        <div class="back-to-top-btn" v-show="showBackToTopBtn" @click="scrollToTop">
                            <v-icon color="white">mdi-arrow-up</v-icon>
                        </div>
                    </div>

                    <div class="chat-input">
                        <div class="chat-input-container">
                            <v-text-field v-model="userMessage"
                                :placeholder="selectedMode.value == 'v2' ? '请输入深度检索内容...' : '请输入您的问题...'"
                                @keyup.enter="!isProcessing && userMessage.trim() && sendMessage()" hide-details
                                variant="outlined" density="comfortable" bg-color="white" :disabled="false"
                                class="input-field" :class="{ 'v2-input': selectedMode.value == 'v2' }">
                            </v-text-field>
                        </div>
                        <div class="bottom-input-row">
                            <div class="mode-selector">
                                <v-select v-model="selectedMode" @update:model-value="handleModeChange"
                                    :items="modeOptions" variant="plain" density="compact" hide-details
                                    class="mode-select" :disabled="isProcessing" bg-color="transparent" return-object
                                    item-title="text" item-value="value" item-value-key="value">
                                    <template v-slot:selection="{ item }">
                                        <div class="d-flex align-center mode-label">
                                            <v-icon size="x-small" color="primary" class="mr-1">
                                                {{ item.raw.icon }}
                                            </v-icon>
                                            {{ item.raw.text }}
                                        </div>
                                    </template>
                                    <template v-slot:item="{ props, item }">
                                        <v-list-item v-bind="props">
                                            <template v-slot:prepend>
                                                <v-icon size="x-small" color="primary">
                                                    {{ item.raw.icon }}
                                                </v-icon>
                                            </template>
                                        </v-list-item>
                                    </template>
                                </v-select>
                            </div>

                            <!-- 知识库选择器 - 在日常问答、内容创作、政策问答模式下显示，创城工作报告模式不显示 -->
                            <div class="kb-selector"
                                v-if="selectedMode.value === 'r1' || selectedMode.value === 'content_creation' || selectedMode.value === 'policy_qa'">
                                <v-select v-model="selectedKnowledgeBase" :items="knowledgeBaseOptions" variant="plain"
                                    density="compact" hide-details class="kb-select" :disabled="isProcessing"
                                    bg-color="transparent" return-object item-title="text" item-value="value"
                                    @update:model-value="handleKnowledgeBaseChange">
                                    <template v-slot:selection="{ item }">
                                        <div class="d-flex align-center kb-label">
                                            <v-icon size="x-small" color="primary" class="mr-1">
                                                mdi-database
                                            </v-icon>
                                            <span class="kb-label-text">{{ item.raw.text }}</span>
                                        </div>
                                    </template>
                                    <template v-slot:item="{ props }">
                                        <v-list-item v-bind="props">
                                            <template v-slot:prepend>
                                                <v-icon size="small" color="primary">
                                                    mdi-database
                                                </v-icon>
                                            </template>
                                        </v-list-item>
                                    </template>
                                </v-select>
                            </div>

                            <!-- 联网按钮 - 只在内容创作模式下显示 -->
                            <div class="network-selector" v-if="selectedMode.value === 'content_creation'">
                                <v-btn :variant="isNetworkEnabled ? 'flat' : 'outlined'"
                                    :color="isNetworkEnabled ? 'success' : 'grey'" size="small" density="compact"
                                    :disabled="isProcessing" @click="toggleNetwork" class="network-btn">
                                    <v-icon size="x-small" class="mr-1">
                                        {{ isNetworkEnabled ? 'mdi-wifi' : 'mdi-wifi-off' }}
                                    </v-icon>
                                    {{ isNetworkEnabled ? '联网' : '联网' }}
                                </v-btn>
                            </div>

                            <div class="input-disclaimer" :class="{ 'v2-disclaimer': selectedMode.value == 'v2' }">
                                <v-icon size="small" :color="selectedMode.value == 'v2' ? 'green-darken-1' : 'grey'">
                                    {{ selectedMode.value == 'v2' ? 'mdi-chart-box-outline' :
                                        'mdi-information-outline' }}
                                </v-icon>
                                <span>{{ getInputDisclaimer() }}</span>
                            </div>

                            <button class="send-button" :class="{ 'processing': isProcessing }"
                                @click="handleButtonClick" :disabled="!userMessage.trim() && !isProcessing">
                                {{ isProcessing ? '停止' : '发送' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 清除对话确认对话框 -->
            <v-dialog v-model="showClearConfirmDialog" :max-width="$vuetify.display.smAndDown ? '95%' : '400'">
                <v-card>
                    <v-card-title class="bg-warning text-white">确认清除</v-card-title>
                    <v-card-text class="pa-4 pt-5">
                        是否确认清除当前对话？这将删除所有聊天记录，但会保留到历史记录中。
                    </v-card-text>
                    <v-card-actions class="pa-4 pt-0">
                        <v-spacer></v-spacer>
                        <v-btn color="grey" variant="text" @click="showClearConfirmDialog = false">取消</v-btn>
                        <v-btn color="warning" @click="confirmClearConversation">确认清除</v-btn>
                    </v-card-actions>
                </v-card>
            </v-dialog>

            <!-- Toast通知 -->
            <div v-if="toast.show" class="toast-container">
                <div class="toast" :class="toast.type">
                    <v-icon size="small" color="white" class="mr-2">
                        {{
                            toast.type === 'success' ? 'mdi-check-circle' :
                                toast.type === 'error' ? 'mdi-alert-circle' :
                                    toast.type === 'warning' ? 'mdi-alert' :
                                        'mdi-information'
                        }}
                    </v-icon>
                    {{ toast.message }}
                </div>
            </div>

            <!-- 重命名对话框 -->
            <v-overlay v-model="showRenameDialog" class="align-center justify-center" scrim="rgba(0,0,0,0.5)">
                <div class="rename-dialog">
                    <v-card min-width="400" max-width="90vw">
                        <v-card-title class="bg-primary text-white d-flex align-center">
                            <v-icon color="white" class="mr-2">mdi-pencil</v-icon>
                            重命名对话
                        </v-card-title>

                        <v-card-text class="pa-4">
                            <v-text-field v-model="newHistoryTitle" label="请输入新名称" variant="outlined"
                                density="comfortable" hide-details autofocus
                                @keyup.enter="newHistoryTitle.trim() && confirmRename()"
                                @keyup.esc="showRenameDialog = false"></v-text-field>
                        </v-card-text>

                        <v-card-actions class="pa-4 pt-0">
                            <v-spacer></v-spacer>
                            <v-btn variant="text" @click="showRenameDialog = false">取消</v-btn>
                            <v-btn color="primary" @click="confirmRename" :disabled="!newHistoryTitle.trim()">确认</v-btn>
                        </v-card-actions>
                    </v-card>
                </div>
            </v-overlay>

            <!-- 删除确认对话框 -->
            <v-dialog v-model="showDeleteConfirmDialog" class="delete-dialog align-center justify-center"
                max-width="400" max-height="170" persistent>
                <v-card class="delete-dialog-card">
                    <v-card-title class="bg-error text-white">确认删除</v-card-title>
                    <v-card-text class="pa-4 pt-5">
                        是否确认删除该历史记录？此操作不可恢复。
                    </v-card-text>
                    <v-card-actions class="pa-4 pt-0">
                        <v-spacer></v-spacer>
                        <v-btn variant="text" @click="showDeleteConfirmDialog = false">取消</v-btn>
                        <v-btn color="error" @click="confirmDelete">确认删除</v-btn>
                    </v-card-actions>
                </v-card>
            </v-dialog>

            <!-- 清空历史记录确认对话框 -->
            <v-dialog v-model="showClearHistoryConfirmDialog" class="delete-dialog align-center justify-center"
                max-width="400" max-height="170" persistent>
                <v-card class="delete-dialog-card">
                    <v-card-title class="bg-warning text-white">确认清空</v-card-title>
                    <v-card-text class="pa-4 pt-5">
                        是否确认清空所有历史记录？此操作不可恢复。
                    </v-card-text>
                    <v-card-actions class="pa-4 pt-0">
                        <v-spacer></v-spacer>
                        <v-btn variant="text" @click="showClearHistoryConfirmDialog = false">取消</v-btn>
                        <v-btn color="warning" @click="confirmClearHistory">确认清空</v-btn>
                    </v-card-actions>
                </v-card>
            </v-dialog>

            <!-- 修改菜单弹出层 -->
            <v-menu v-model="historyMenuOpen" :close-on-content-click="false" location="bottom"
                :position-x="menuPosition.x" :position-y="menuPosition.y" absolute min-width="180">
                <v-list density="compact" class="history-menu-list">
                    <!-- <v-list-item @click="renameHistory(selectedHistoryItem)" class="history-menu-list-item">
            <template v-slot:prepend>
              <v-icon size="small" color="primary">mdi-pencil</v-icon>
            </template>
            <v-list-item-title>重命名</v-list-item-title>
          </v-list-item> -->
                    <v-divider class="my-1"></v-divider>
                    <v-list-item @click="deleteHistory(selectedHistoryItem)" class="history-menu-list-item">
                        <template v-slot:prepend>
                            <v-icon size="small" color="error">mdi-delete</v-icon>
                        </template>
                        <v-list-item-title class="text-error">删除</v-list-item-title>
                    </v-list-item>
                </v-list>
            </v-menu>

            <!-- 移动端遮罩层 -->
            <div class="sidebar-overlay" v-if="isLeftSidebarOpen" @click="toggleLeftSidebar"></div>

            <!-- 成员管理抽屉 -->
            <v-navigation-drawer v-model="memberDrawerOpen" location="right" temporary width="1400"
                class="member-drawer">
                <div class="drawer-header">
                    <div class="drawer-title">{{ currentKnowledgeBase }}成员管理</div>
                    <v-btn icon variant="text" @click="memberDrawerOpen = false">
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                </div>

                <div class="drawer-content">
                    <!-- 重构搜索栏的HTML结构 -->
                    <div class="search-bar">
                        <div class="search-inputs">
                            <input class="search-input" v-model="memberSearchQuery" placeholder="请输入人名、手机号等..." />

                            <el-select v-model="memberStatusFilter" class="filter-select" size="default"
                                placeholder="全部">
                                <el-option v-for="item in statusOptions" :key="item" :value="item" :label="item" />
                            </el-select>
                        </div>

                        <div class="action-buttons">
                            <button class="action-btn add-btn" @click="addNewMember">
                                添加成员
                            </button>
                            <button class="action-btn delete-btn" @click="deleteSelectedMembers"
                                :disabled="!hasSelectedMembers">
                                删除成员<span v-if="hasSelectedMembers">({{ selectedMembers.length }})</span>
                            </button>
                        </div>
                    </div>

                    <div class="members-table">
                        <el-table :data="filteredMembers" style="width: 100%" v-loading="isLoading"
                            @selection-change="handleSelectionChange">
                            <el-table-column type="selection" width="55" />
                            <el-table-column label="姓名" width="180">
                                <template #default="scope">
                                    <div class="member-info">
                                        <v-avatar :color="scope.row.avatarColor" size="32">
                                            <span class="text-white">{{ scope.row.name.charAt(0).toUpperCase() }}</span>
                                        </v-avatar>
                                        <span>{{ scope.row.name }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="账号状态" width="120">
                                <template #default="scope">
                                    <span class="status-tag"
                                        :class="scope.row.status == '正常' ? 'status-normal' : 'status-disabled'">
                                        {{ scope.row.status }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column label="手机号码" prop="phone" width="150" />
                            <el-table-column label="加入时间" prop="joinDate" width="180" />
                            <el-table-column label="角色" width="120">
                                <template #default="scope">
                                    <el-dropdown trigger="click"
                                        @command="(command) => handleRoleChange(scope.row, command)">
                                        <v-btn v-if="scope.row.role == '管理员'" variant="text" color="primary"
                                            size="small" class="role-btn">
                                            本知识库管理员
                                            <v-icon size="small">mdi-chevron-down</v-icon>
                                        </v-btn>
                                        <v-btn v-else variant="text" color="grey" size="small" class="role-btn">
                                            本知识库成员
                                            <v-icon size="small">mdi-chevron-down</v-icon>
                                        </v-btn>
                                        <template #dropdown>
                                            <el-dropdown-menu>
                                                <el-dropdown-item :command="1"
                                                    :disabled="scope.row.role == '管理员'">设为本知识库管理员</el-dropdown-item>
                                                <el-dropdown-item :command="2"
                                                    :disabled="scope.row.role == '成员'">设为本知识库成员</el-dropdown-item>
                                            </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作">
                                <template #default="scope">
                                    <v-btn variant="text" color="grey" size="small" @click="deleteMember(scope.row)">
                                        删除成员
                                    </v-btn>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>

                    <div class="pagination-controls">
                        <div class="page-info">
                            共 <span>{{ totalMembersCount }}</span> 条记录
                        </div>

                        <div class="pagination-wrapper">
                            <button v-for="size in [10, 20, 50, 100]" :key="size" @click="handlePageSizeChange(size)"
                                :class="['page-size-btn', pageSize === size ? 'active' : '']">
                                {{ size }}条/页
                            </button>

                            <button class="page-btn" @click="currentPage = 1" :disabled="currentPage === 1">
                                <span class="page-nav-icon">«</span>
                            </button>
                            <button class="page-btn" @click="handlePrevPage()" :disabled="currentPage === 1">
                                <span class="page-nav-icon">‹</span>
                            </button>

                            <span class="page-info-text">{{ currentPage }} / {{ Math.ceil(totalMembersCount / pageSize)
                            }}</span>

                            <button class="page-btn" @click="handleNextPage()"
                                :disabled="currentPage >= Math.ceil(totalMembersCount / pageSize)">
                                <span class="page-nav-icon">›</span>
                            </button>
                            <button class="page-btn" @click="currentPage = Math.ceil(totalMembersCount / pageSize)"
                                :disabled="currentPage >= Math.ceil(totalMembersCount / pageSize)">
                                <span class="page-nav-icon">»</span>
                            </button>

                            <span class="goto-text">前往</span>
                            <input class="goto-input" type="number" v-model="goToPage" min="1"
                                :max="Math.ceil(totalMembersCount / pageSize)">
                            <span class="page-text">页</span>
                            <button class="goto-btn" @click="navigateToPage()">确定</button>
                        </div>
                    </div>
                </div>
            </v-navigation-drawer>

            <!-- 添加用户对话框 -->
            <v-dialog style="z-index: 30000 !important;" v-model="showAddUserDialog" width="900px" persistent>
                <v-card class="add-user-dialog">
                    <div class="dialog-header">
                        <div class="dialog-title">选择用户</div>
                        <v-btn icon variant="text" @click="showAddUserDialog = false">
                            <v-icon>mdi-close</v-icon>
                        </v-btn>
                    </div>
                    <div class="dialog-content">
                        <!-- 搜索区域 -->
                        <div class="search-area">
                            <div class="search-item">
                                <span class="search-label">用户名称</span>
                                <input class="search-input" v-model="userSearchQuery.name" placeholder="请输入用户名称" />
                            </div>
                            <div class="search-item">
                                <span class="search-label">手机号码</span>
                                <input class="search-input" v-model="userSearchQuery.phone" placeholder="请输入手机号码" />
                            </div>
                            <div class="search-buttons">
                                <button class="search-btn" @click="searchUsers">
                                    <v-icon size="small">mdi-magnify</v-icon> 搜索
                                </button>
                                <button class="reset-btn" @click="resetUserSearch">
                                    <v-icon size="small">mdi-refresh</v-icon> 重置
                                </button>
                            </div>
                        </div>

                        <!-- 用户表格 -->
                        <div class="user-table-container">
                            <table class="user-table">
                                <thead>
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" v-model="selectAllUsers"
                                                @change="toggleSelectAllUsers" />
                                        </th>
                                        <th>用户名称</th>
                                        <th>用户账号</th>
                                        <th>已加入的知识库</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="user in filteredUsers" :key="user.userId">
                                        <td>
                                            <input type="checkbox" v-model="user.selected" />
                                        </td>
                                        <td>{{ user.nickName }}</td>
                                        <td>{{ user.userName || user.userId }}</td>
                                        <td>
                                            <div class="knowledge-base-tags"
                                                style="display: flex; flex-wrap: wrap; gap: 4px;">
                                                <span
                                                    v-if="!user.knowledgeBaseNames || user.knowledgeBaseNames.length === 0"
                                                    style="color: #999;">无</span>
                                                <span v-else
                                                    v-for="(kb, kbIndex) in (user.knowledgeBaseNames || '').split(',')"
                                                    :key="kbIndex"
                                                    style="background-color: #f0f9ff; color: #1677ff; padding: 2px 8px; border-radius: 4px; font-size: 12px;">
                                                    {{ kb }}
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="status-tag"
                                                :class="user.status == '0' ? 'status-normal' : 'status-disabled'">
                                                {{ user.status == '0' ? '正常' : '停用' }}
                                            </span>
                                        </td>
                                        <td>{{ user.createTime || '-' }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页控件 -->
                        <div class="pagination-area">
                            <div class="pagination-wrapper">
                                <span class="total-text">共 {{ totalUsers }} 条</span>
                                <el-select v-model="userPageSize" class="page-size-select" size="small">
                                    <el-option value="10" label="10条/页" />
                                </el-select>
                                <el-pagination v-model:current-page="currentUserPage" :page-size="userPageSize"
                                    layout="prev, pager, next, jumper" :total="totalUsers" :pager-count="7" background
                                    @current-change="handleUserPageChange" />
                            </div>
                        </div>
                    </div>

                    <div class="dialog-footer">
                        <button class="cancel-button" @click="showAddUserDialog = false">取消</button>
                        <button class="confirm-button" @click="confirmAddUser" :disabled="!hasSelectedUsers">确定
                            <span v-if="hasSelectedUsers">({{userSelectOptions.filter(u => u.selected).length}})</span>
                        </button>
                    </div>
                </v-card>
            </v-dialog>

            <!-- 上传对话框 -->
            <v-dialog v-model="showUploadDialog" max-width="600px" persistent>
                <v-card class="upload-dialog">
                    <div class="dialog-header">
                        <div class="dialog-title">上传文件</div>
                        <v-btn icon variant="text" @click="showUploadDialog = false">
                            <v-icon>mdi-close</v-icon>
                        </v-btn>
                    </div>
                    <div class="dialog-content">
                        <div class="file-upload-area" :class="{ 'drag-over': isDragOver }" @dragover="handleDragOver"
                            @dragleave="handleDragLeave" @drop="handleDrop" @click="triggerFileInput">
                            <input ref="fileInput" type="file" style="display: none" @change="handleFileInputChange" />
                            <v-icon size="64" color="grey">mdi-cloud-upload-outline</v-icon>
                            <div class="upload-text">
                                <p>点击或拖拽文件到此处上传</p>
                                <p class="upload-hint">支持单个文件上传</p>
                                <p class="upload-hint" style="font-size: 12px; color: #666;">
                                    支持的文件格式：docx, xlsx, xls, ppt, pptx, pdf, txt, jpeg, jpg, png, tif, gif, csv, json,
                                    eml, html,
                                    doc
                                </p>
                            </div>
                        </div>

                        <!-- 已选文件信息 -->
                        <div v-if="selectedFiles.length > 0" class="selected-file-info">
                            <div class="selected-file">
                                <v-icon color="primary" size="small">mdi-file-outline</v-icon>
                                <span class="file-name">{{ selectedFiles[0].name }}</span>
                                <span class="file-size">{{ formatFileSize(selectedFiles[0].size) }}</span>
                                <v-btn icon variant="text" size="small" @click="clearSelectedFiles()">
                                    <v-icon>mdi-close</v-icon>
                                </v-btn>
                            </div>
                        </div>

                        <!-- 添加文件权限选择 -->
                        <div class="permission-selection" style="margin-top: 16px;margin-left: 20px;">
                            <div class="permission-title" style="margin-bottom: 10px; font-weight: 600; color: #333;">
                                文件权限</div>
                            <div class="permission-options" style="display: flex; gap: 24px;">
                                <!-- 只有当用户有部门ID且已加入知识库时才显示"私密"选项 -->
                                <label v-if="userStore.users && userStore.users.deptId && isJoin"
                                    style="display: flex; align-items: center; cursor: pointer;">
                                    <input type="radio" v-model="filePermissionType" value="REPO"
                                        style="margin-right: 10px; accent-color: #F34343;">
                                    <span style="color: #F34343; font-weight: 500;">私密（仅部门可见）</span>
                                </label>
                                <label style="display: flex; align-items: center; cursor: pointer;">
                                    <input type="radio" v-model="filePermissionType" value="ALL"
                                        style="margin-right: 10px; accent-color: #0288D1;">
                                    <span style="color: #0288D1; font-weight: 500;">共享（单位内可见）</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="dialog-footer">
                        <v-btn variant="outlined" @click="showUploadDialog = false" class="cancel-btn">取消</v-btn>
                        <v-btn color="#f5f5f5" @click="uploadFiles" class="confirm-btn"
                            :disabled="selectedFiles.length == 0">开始上传处理</v-btn>
                    </div>
                </v-card>
            </v-dialog>

            <!-- 文档切片对话框 -->
            <el-dialog v-model="showSlicingDialog" :title="`文档切片(${sectionValname})`" width="500px"
                :close-on-click-modal="false" :append-to-body="true" :destroy-on-close="true"
                class="slicing-dialog-container" align-center :modal-class="'slicing-dialog-modal'" top="40vh">
                <div class="dialog-content">
                    <p class="mb-2">选择切片解析器：</p>
                    <!-- 使用普通的HTML select替代Element Plus组件 -->
                    <el-select v-model="selectedParser" placeholder="请选择解析器" size="large" style="width: 350px"
                        @change="handleParserChange">
                        <el-option v-for="item in parserOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                    <!-- <select
            v-model="selectedParser"
            class="custom-select"
            :disabled="loadingParsers"
            @change="handleParserChange"
          >
            <option value="" disabled>请选择切片解析器</option>
            <option
              v-for="item in parserOptions"
              :key="item.value"
              :value="item.value"
            >{{ item.label }} [ID: {{item.value}}]</option>
          </select> -->
                    <div v-if="loadingParsers" class="loading-text">加载中...</div>
                </div>

                <template #footer>
                    <span class="dialog-footer">

                        <el-button @click="showSlicingDialog = false">取消</el-button>
                        <el-button type="primary" @click="confirmSlicing"
                            :disabled="selectedParser === '' || processingSlicing">
                            {{ processingSlicing ? '处理中...' : '确定' }}
                        </el-button>
                    </span>
                </template>
            </el-dialog>
            <!-- 删除上传进度对话框 -->
        </div>
    </div>
</template>
<script setup>
import { reposList } from '@/api/menu';
import { addFile, getFileList, getDownloadUrl, downloadFile2, ragDeleteFile, upFile, docMove, RemoveRagFile, docuDelete, findSlicing, slicingDoc, chunksDoc } from '@/api/file/file.js';
import { addHistory, addMessage, getMessage, removeMessage } from '@/api/chat';
import { userListApi, MembersAdd, MembersList, MembersAddBatch, MembersEdit, MembersRemove } from '@/api/user'; // 添加用户API导入
import { ref, onMounted, onUnmounted, nextTick, watch, computed, getCurrentInstance, watchEffect } from 'vue';
// API 配置
import { BackTop, Ellipsis } from 'vue-amazing-ui'
import { baseURL } from '@/utils/request'
import 'vue-amazing-ui/es/backtop/BackTop.css'
import 'vue-amazing-ui/es/ellipsis/Ellipsis.css'
import { getApiBaseUrl, getApiToken, CHAT_ID, AGENT_ID, AGENT_ID_NR, AGENT_ID_ZC, AGENT_ID_BG, DATASET_ID, ENV, ENV_BG } from '@/config/api.js';
import useUserStore from '@/store/modules/user';
import axios from 'axios';
import officePreview from "@/components/officePreview.vue";
import PDFView from '@/components/pdfView.vue';
import { ElMessage } from 'element-plus';
import { ElInfiniteScroll, ElSkeleton, ElSkeletonItem, ElTable, ElTableColumn, ElPagination, ElLoading, ElBacktop } from 'element-plus';
import hljs from 'highlight.js/lib/common'; // 导入常用语言包
import 'highlight.js/styles/github.css'; // 更改为 GitHub 主题
import { marked } from 'marked'; // 导入marked库用于Markdown解析
import '@/styles/markdown.css'; // 导入Markdown样式
import { getToken } from '@/utils/auth';
import { decrypt } from '@/utils/jsencrypt';  //公钥解密
import { blobValidate } from '@/utils/ruoyi';
import { editFile } from '@/api/file/file.js';
import { getPublicKey } from '@/api/login';
import useUploadStore from '@/store/modules/upload';
// 获取当前组件实例，用于访问proxy
const { proxy } = getCurrentInstance();
const selectedParser = ref('');
const defaultCustomStyle = {
    '--backtop-width': '60px',
    '--backtop-height': '60px',
    '--backtop-icon-size': '32px',
    '--backtop-primary-bg-color-hover': '#dd6747',
    '--backtop-primary-shadow-color': 'rgba(221, 103, 71, 0.36)',
    '--backtop-primary-shadow-color-hover': 'rgba(221, 103, 71, 0.36)',
    '--backtop-square-border-radius': '18px'
}
// 获取上传store
const uploadStore = useUploadStore();
const fileInfo = ref({})
const sectionVal = ref('');
const sectionValname = ref('');
// 获取用户store
const userStore = useUserStore();

// 文件状态轮询定时器
let fileStatusPollingInterval = null;

// 检查是否有处理中的文件
const hasProcessingFiles = () => {
    return fileList.value.some(file =>
        file.status === 'PARSING' || file.status === 'PROCESSING' || file.status === 'UPLOADING'
    );
};

// 启动文件状态轮询 - 优化为无感刷新
const startFileStatusPolling = () => {
    // 如果已经有定时器在运行，先清除它
    if (fileStatusPollingInterval) {
        clearInterval(fileStatusPollingInterval);
    }

    // 创建新的定时器，每5秒检查一次
    fileStatusPollingInterval = setInterval(async () => {
        if (hasProcessingFiles()) {
            try {
                // 调用getFileList接口获取最新文件列表
                const params = {
                    repoId: repoIds.value, // 当前知识库ID
                    pageNum: 1,
                    pageSize: 50 // 获取足够多的文件确保能覆盖当前显示的所有文件
                };

                const res = await getFileList(params);

                if (res.code === 0 && res.data && res.data.list) {
                    // 获取新文件列表
                    const newFiles = res.data.list;

                    // 遍历当前文件列表，更新文件状态
                    fileList.value.forEach((currentFile, index) => {
                        // 在新文件列表中查找匹配的文件
                        const newFile = newFiles.find(f => f.fileId === currentFile.fileId);

                        if (newFile) {
                            // 只更新状态相关的字段，保持其他字段不变
                            fileList.value[index].status = newFile.status;
                            fileList.value[index].progress = newFile.status === 'PARSE_SUCCESS' ? 100 : currentFile.progress;
                            fileList.value[index].documentSummary = newFile.documentSummary || currentFile.documentSummary;
                            fileList.value[index].keywords = newFile.keywords || currentFile.keywords;

                            // 如果文件状态从处理中变为完成，显示提示
                            if (currentFile.status === 'PARSING' && newFile.status === 'PARSE_SUCCESS') {
                                showToast(`文件 ${currentFile.fileName} 处理完成`, 'success');
                            }
                        }
                    });

                    // 检查是否还有处理中的文件
                    if (!hasProcessingFiles()) {
                        clearInterval(fileStatusPollingInterval);
                        fileStatusPollingInterval = null;
                    }
                }
            } catch (error) {
                console.error('轮询刷新文件状态出错:', error);
            }
        } else {
            // 如果没有处理中的文件，停止轮询
            clearInterval(fileStatusPollingInterval);
            fileStatusPollingInterval = null;
        }
    }, 5000); // 每5秒执行一次
};

// 停止文件状态轮询
const stopFileStatusPolling = () => {
    if (fileStatusPollingInterval) {
        clearInterval(fileStatusPollingInterval);
        fileStatusPollingInterval = null;
    }
};

// 检查并获取publicKey
const checkAndGetPublicKey = async () => {
    if (!userStore.publicKey) {
        try {
            const result = await getPublicKey();

            if (result && result.publicKey) {
                // 直接使用API返回的publicKey，不做任何处理
                userStore.setPublicKey(result.publicKey);
                return result.publicKey;
            } else {
                console.error('获取publicKey失败，返回数据格式不正确:', result);
                return null;
            }
        } catch (error) {
            console.error('获取publicKey出错:', error);
            return null;
        }
    } else {
        return userStore.publicKey;
    }
};

// 打印当前登录用户信息
const printCurrentUserInfo = () => {
    // 用户信息已在控制台输出，此处保留函数结构
};

// 添加状态显示相关的函数
const getStatusText = (status) => {
    switch (status) {
        case 'UPLOADING': return '上传中';
        case 'PARSING': return '向量化处理中';
        case 'PROCESSING': return '向量化处理中'; // 添加处理中状态
        case 'PARSE_SUCCESS': return '解析成功';
        case 'PARSE_FAILED': return '解析失败';
        case 'UPLOAD_TIMEOUT': return '上传超时';
        case 'UPLOAD_ERROR': return '上传失败';
        case 'UNPARSED': return '未解析';
        default: return '未知状态';
    }
};

// 根据文件进度返回上传状态文本，不依赖状态判断
const getFileUploadStatusText = (file) => {
    if (!file) return '准备上传...';

    const progress = file.progress || 0;

    // 直接检测并启动自动进度
    if (file.status === 'UPLOADING' && progress > 0 && progress < 98) {
        // 立即创建自动进度定时器（如果不存在）
        if (!window['autoProgress_' + file.id]) {
            console.log('状态文本函数中检测到上传中文件，立即启动自动进度');

            // 创建定时器确保进度持续增加
            window['autoProgress_' + file.id] = setInterval(() => {
                // 查找文件
                const fileIndex = fileList.value.findIndex(item => item.id === file.id);
                if (fileIndex !== -1) {
                    // 获取当前文件
                    const currentFile = fileList.value[fileIndex];

                    // 如果已完成或不再上传中，停止定时器
                    if (currentFile.status !== 'UPLOADING' || currentFile.progress >= 98) {
                        clearInterval(window['autoProgress_' + file.id]);
                        window['autoProgress_' + file.id] = null;

                        return;
                    }

                    // 根据当前进度确定增加速度
                    let increment = 0.8; // 默认增长率更快
                    const currentProgress = currentFile.progress;

                    // 进度越高，增加越慢
                    if (currentProgress > 80) increment = 0.1; // 80%后显著减慢
                    else if (currentProgress > 50) increment = 0.5; // 50-80%之间中等速度

                    // 更新进度（不超过98%）
                    const newProgress = Math.min(currentProgress + increment, 98);
                    fileList.value[fileIndex].progress = newProgress;

                    // 强制视图更新 - 通过创建对象副本
                    fileList.value[fileIndex] = { ...fileList.value[fileIndex] };


                } else {
                    // 找不到文件，清除定时器
                    clearInterval(window['autoProgress_' + file.id]);
                    window['autoProgress_' + file.id] = null;
                }
            }, 300); // 每0.3秒更新一次，更流畅
        }
    }

    // 动态生成省略号，确保动画效果
    const getDots = () => '.'.repeat(1 + Math.floor(Date.now() / 300) % 3);

    // 仅根据进度百分比判断
    if (progress === 100) {
        return '处理完成 100%';
    } else if (progress >= 99) {
        // 99%时显示"结构化处理中"加省略号动画效果
        return `结构化处理中${getDots()} 99%`;
    } else if (progress >= 75) {
        return `结构化处理中${getDots()} ${Math.round(progress)}%`;
    } else if (progress >= 50) {
        return `文件解析中${getDots()} ${Math.round(progress)}%`;
    } else {
        return `文件上传中${getDots()} ${Math.round(progress)}%`;
    }
};
const repoIds = ref(''); //点击选择的知识库id
const getStatusBgColor = (status) => {
    switch (status) {
        case 'UPLOADING': return '#E3F2FD'; // 浅蓝色背景
        case 'PARSING': return '#FFF8E1'; // 浅黄色背景
        case 'PROCESSING': return '#E8F5E9'; // 浅绿色背景(处理中)
        case 'PARSE_SUCCESS': return '#DCFCE7'; // 浅绿色背景
        case 'PARSE_FAILED': return '#FFEBEE'; // 浅红色背景
        case 'UPLOAD_TIMEOUT': return '#FFEBEE'; // 浅红色背景
        case 'UPLOAD_ERROR': return '#FFEBEE'; // 浅红色背景
        case 'UNPARSED': return '#F3F4F6'; // 浅灰色背景
        default: return '#F5F5F5'; // 浅灰色背景
    }
};

const getStatusTextColor = (status) => {
    switch (status) {
        case 'UPLOADING': return '#1565C0'; // 蓝色文字
        case 'PARSING': return '#FF8F00'; // 橙色文字
        case 'PROCESSING': return '#2E7D32'; // 绿色文字(处理中)
        case 'PARSE_SUCCESS': return '#166534'; // 绿色文字
        case 'PARSE_FAILED': return '#C62828'; // 红色文字
        case 'UPLOAD_TIMEOUT': return '#C62828'; // 红色文字
        case 'UPLOAD_ERROR': return '#C62828'; // 红色文字
        case 'UNPARSED': return '#6B7280'; // 灰色文字
        default: return '#757575'; // 灰色文字
    }
};

// 判断用户是否有权限删除文件
const canDeleteFile = (file) => {
    // 如果没有文件数据，返回false
    if (!file) return false;

    // 情况1：最高级管理员可以删除所有文件
    if (isHighLevelAdmin.value) {
        return true;
    }

    // 情况2：知识库管理员可以删除该知识库的所有文件（私有或共享）
    if (isCurrentRepoAdmin.value && isJoinedCurrentRepo.value) {
        return true;
    }

    // 情况3：普通成员只能删除自己上传的文件
    return file.userId === userStore.id;
};

// 判断用户是否属于文件所在的部门
const isUserInFileDepartment = (file) => {
    if (!file || !file.deptId || !userStore.users || !userStore.users.deptId) return false;

    // 如果用户部门ID和文件部门ID相同，则用户属于文件所在部门
    return file.deptId === userStore.users.deptId;
};

// 判断批量操作中是否可以显示删除按钮
const canShowBatchDeleteButton = computed(() => {
    // 如果没有选中的文件，不显示
    if (!hasSelectedFiles.value) return false;

    // 情况1：最高级管理员可以删除所有文件
    if (isHighLevelAdmin.value) {
        return true;
    }

    // 情况2：知识库管理员可以删除该知识库的所有文件（私有或共享）
    if (isCurrentRepoAdmin.value && isJoinedCurrentRepo.value) {
        return true;
    }

    // 情况3：普通成员只能删除自己上传的文件
    return fileList.value.some(file => file.selected && file.userId === userStore.id);
});

// 打开文档切片对话框
const openSlicingDialog = (file) => {
    console.log('打开文档切片对话框，当前文件:', file);
    sectionVal.value = file.section ? file.section : '当前文件暂无切片方式';



    currentSlicingFile.value = file;

    // 先重置状态
    selectedParser.value = '';
    parserOptions.value = [];

    // 立即显示对话框，避免用户等待
    showSlicingDialog.value = true;

    // 然后加载数据
    loadingParsers.value = true;

    // 调用findSlicing接口获取切片解析器列表
    console.log('调用findSlicing API，参数:', { fileName: file.fileName });
    findSlicing({ fileName: file.fileName })
        .then(res => {
            console.log('获取切片解析器响应:', res);

            console.log('切片解析器响应数据结构:', JSON.stringify(res.data, null, 2));
            // 详细分析返回的数据结构
            if (res.data) {
                if (sectionVal.value) {
                    // 查找匹配的解析器，支持多种ID字段格式
                    const matchedParser = res.data.find(item => {
                        const id = item.id || item.parserId || item.value || '';
                        return id == sectionVal.value || id.toString() === sectionVal.value.toString();
                    });
                    sectionValname.value = matchedParser?.name || matchedParser?.label || '';
                    console.log('当前文件切片方式为：', sectionValname.value);
                }

                sectionValname.value = sectionValname.value ? '当前文件切片方式为：' + sectionValname.value : '当前文件暂无切片方式';
                if (Array.isArray(res.data)) {
                    console.log('返回数据是数组，长度:', res.data.length);
                    if (res.data.length > 0) {
                        console.log('第一项数据结构:', res.data[0]);
                    }
                } else {
                    console.log('返回数据是对象，结构:', Object.keys(res.data));
                    if (res.data.data && Array.isArray(res.data.data)) {
                        console.log('data字段是数组，长度:', res.data.data.length);
                        if (res.data.data.length > 0) {
                            console.log('第一项数据结构:', res.data.data[0]);
                        }
                    }
                }
            }

            if (res.code === 0 && res.data) {
                // 检查res.data的结构，确保正确处理
                if (Array.isArray(res.data)) {
                    // 如果是数组结构，直接映射
                    parserOptions.value = res.data.map(parser => {
                        // 确保每个解析器具有id和name属性，适应不同的API返回格式
                        const id = parser.id || parser.parserId || parser.value || '';
                        const name = parser.name || parser.label || '解析器' + id;
                        return {
                            value: id,
                            label: name
                        };
                    });
                } else if (typeof res.data === 'object') {
                    // 如果是对象结构，尝试转换为数组
                    // 这里假设返回结构可能是 {data: [{id: 1, name: 'xxx'}, ...]} 或类似结构
                    const dataArray = res.data.data || res.data.list || Object.values(res.data);
                    if (Array.isArray(dataArray)) {
                        parserOptions.value = dataArray.map(parser => {
                            // 确保每个解析器具有id和name属性，适应不同的API返回格式
                            const id = parser.id || parser.parserId || parser.value || '';
                            const name = parser.name || parser.label || '解析器' + id;
                            return {
                                value: id,
                                label: name
                            };
                        });
                    } else {
                        console.error('解析器数据格式异常:', res.data);
                        showToast('解析器数据格式异常', 'error');
                        parserOptions.value = [];
                    }
                } else {
                    console.error('解析器数据格式异常:', res.data);
                    showToast('解析器数据格式异常', 'error');
                    parserOptions.value = [];
                }

                // 输出处理后的选项格式，用于调试
                console.log('转换后的解析器选项格式:', parserOptions.value);

                console.log('处理后的解析器选项:', parserOptions.value);

                // 根据文件已有的切片方式设置默认选中值
                if (parserOptions.value.length > 0) {
                    // 如果文件有已设置的切片方式，尝试匹配对应的解析器
                    if (sectionVal.value && sectionVal.value !== '当前文件暂无切片方式') {
                        const matchedOption = parserOptions.value.find(option =>
                            option.value === sectionVal.value ||
                            option.value.toString() === sectionVal.value.toString()
                        );

                        if (matchedOption) {
                            console.log('找到匹配的解析器，设置为默认选中:', matchedOption);
                            selectedParser.value = matchedOption.value;
                        } else {
                            console.log('未找到匹配的解析器，使用第一个选项作为默认值');
                            selectedParser.value = parserOptions.value[0].value;
                        }
                    } else {
                        // 如果文件没有设置切片方式，使用第一个选项作为默认值
                        console.log('文件无切片方式，设置第一项为默认选中值');
                        selectedParser.value = parserOptions.value[0].value;
                    }

                    console.log('最终设置的默认选中值:', selectedParser.value);
                } else {
                    showToast('没有可用的切片解析器', 'error');
                }
            } else {
                showToast(res.msg || '获取切片解析器失败', 'error');
            }
        })
        .catch(err => {
            console.error('获取切片解析器失败:', err);
            showToast('获取切片解析器失败', 'error');
        })
        .finally(() => {
            loadingParsers.value = false;
        });
};

// 处理解析器选择变化
const handleParserChange = () => {
    console.log('选择变化:', selectedParser.value);
};

// 确认文档切片
const confirmSlicing = () => {
    console.log('确认切片，当前选中的解析器ID:', selectedParser.value);
    console.log('当前文件:', currentSlicingFile.value);

    if (!currentSlicingFile.value) {
        showToast('当前文件信息缺失', 'error');
        return;
    }

    if (!selectedParser.value) {
        console.error('未选择解析器，当前可用选项:', parserOptions.value);
        showToast('请选择切片解析器', 'error');
        return;
    }

    processingSlicing.value = true;

    // 记录当前参数用于调试
    const params = {
        docId: currentSlicingFile.value.fileId,
        parserId: selectedParser.value
    };

    // 额外添加对parserId的检查和日志输出
    console.log('确认选择的解析器ID值:', selectedParser.value);

    console.log('调用slicingDoc接口，参数:', params);

    // 调用slicingDoc接口进行文档切片
    slicingDoc(params)
        .then(res => {
            console.log('切片处理响应:', res);

            if (res.code == 0) {
                showToast('文档切片处理成功');
                showSlicingDialog.value = false;

                // 刷新文件列表
                setTimeout(async () => {
                    await loadKnowledgeBaseFiles(repoIds.value);
                }, 300);
            }
            else if (res.code == -1) {
                showSlicingDialog.value = false;
                showToast('文档切片处理失败', 'error');
            }
            else {
                showToast(res.msg || '切片处理失败', 'error');
                showSlicingDialog.value = false;
            }
        })
        .catch(err => {
            console.error('切片处理出错:', err);
            showToast('切片处理失败', 'error');
            showSlicingDialog.value = false;
        })
        .finally(() => {
            processingSlicing.value = false;
        })
};

// 删除单个文件
const deleteFile = (file) => {
    // 如果文件状态是上传中，直接取消上传并从列表中移除
    if (file.status === 'UPLOADING') {
        // 清除所有相关计时器
        clearInterval(window['progressInterval_' + file.id]);
        clearInterval(window['dotsAnimation_' + file.id]);
        clearInterval(window['autoProgress_' + file.id]); // 清除自动进度定时器

        // 从文件列表中移除
        const index = fileList.value.findIndex(item => item.id === file.id);
        if (index !== -1) {
            fileList.value.splice(index, 1);
        }

        showToast('已取消上传');
        return;
    }

    // 判断用户是否有权限删除该文件
    // 1. 最高级管理员可以删除所有文件
    // 2. 知识库管理员可以删除该知识库内的文件
    // 3. 普通用户只能删除自己上传的文件
    const canDelete = isHighLevelAdmin.value ||
        (isCurrentRepoAdmin.value && isJoinedCurrentRepo.value) ||
        file.userId === userStore.id;

    if (!canDelete) {
        showToast('您没有删除此文件的权限', 'error');
        return;
    }

    // 记录操作日志
    if (isHighLevelAdmin.value) {
        console.log('最高级管理员删除文件');
    } else if (isCurrentRepoAdmin.value) {
        console.log('知识库管理员删除文件');
    } else {
        console.log('文件上传者删除文件');
    }

    // 显示确认对话框
    proxy.$modal.confirm(`确认删除文件 \"${file.fileName || '未命名文件'}\" 吗？`).then(async () => {
        // 这里添加删除文件的API调用
        console.log('删除文件:', file);

        try {
            // 调用docuDelete接口删除文件
            const params = {
                ids: [file.fileId] // 选中的文档ID
            };

            console.log('调用docuDelete，参数:', params);
            // 获取正确的bucketName参数
            let bucketName = null;
            if (repoIds.value) {
                // 如果有选择知识库，查找对应的知识库信息
                const selectedRepo = repoList.value.find(repo => repo.repoId == repoIds.value);
                if (selectedRepo && selectedRepo.remark) {
                    bucketName = selectedRepo.remark;
                }
            }
            // 如果没有知识库信息或是单位知识库，使用userStore.publicBucketName
            if (!bucketName) {
                bucketName = userStore.publicBucketName;
            }

            console.log('删除文件使用的bucketName:', bucketName);
            const response = await RemoveRagFile(params);
            console.log('删除文件响应:', response);

            if (response.code == 0) {
                // 从文件列表中移除该文件
                const index = fileList.value.findIndex(item => item.id === file.id);
                if (index !== -1) {
                    fileList.value.splice(index, 1);
                }

                showToast('文件删除成功');

                // 删除成功后，使用统一方法刷新文件列表
                try {
                    console.log('文件删除成功，准备刷新文件列表');

                    // 延时调用，确保DOM更新完成
                    setTimeout(async () => {
                        // 使用统一的加载方法，传入当前知识库ID
                        await loadKnowledgeBaseFiles(repoIds.value);

                        console.log('文件列表刷新完成');
                    }, 300); // 延长时间确保DOM完全更新
                } catch (error) {
                    console.error('刷新文件列表失败:', error);
                }
            } else {
                showToast(response.msg || '删除文件失败', 'error');
            }
        } catch (error) {
            console.error('删除文件失败:', error);
            showToast('删除文件失败，请重试', 'error');
        }
    }).catch(() => {
        // 用户取消删除
    });
};

// 删除选中的文件
const deleteSelectedFiles = () => {
    // 筛选可删除的文件:
    // - 情况1：最高级管理员可以删除所有文件
    // - 情况2：知识库管理员可以删除该知识库的所有文件
    // - 情况3：普通成员只能删除自己上传的文件
    let selectedFiles = [];

    if (isHighLevelAdmin.value) {
        // 最高级管理员可以删除所有选中的文件
        selectedFiles = fileList.value.filter(file => file.selected);
    } else if (isCurrentRepoAdmin.value && isJoinedCurrentRepo.value) {
        // 知识库管理员可以删除该知识库的所有文件
        selectedFiles = fileList.value.filter(file => file.selected);
    } else {
        // 普通成员只能删除自己上传的文件
        selectedFiles = fileList.value.filter(file => file.selected && (file.createBy == userStore.name || file.userId == userStore.id));
    }
    if (selectedFiles.length == 0) {
        showToast('没有可删除的文件', 'error');
        return;
    }

    // 显示确认对话框
    proxy.$modal.confirm(`确认删除选中的 ${selectedFiles.length} 个文件吗？`).then(async () => {
        // 这里添加批量删除文件的API调用
        console.log('批量删除文件:', selectedFiles);

        let successCount = 0;
        let failCount = 0;

        try {
            // 收集所有要删除的文件ID
            const fileIds = selectedFiles.map(file => file.fileId).filter(id => id);

            if (fileIds.length === 0) {
                showToast('没有有效的文件ID，无法删除', 'error');
                return;
            }

            console.log('准备批量删除文件:', { fileIds });

            // 调用docuDelete接口删除文件
            const params = {
                ids: fileIds // 选中的文档ID数组
            };

            console.log('调用docuDelete批量删除，参数:', params);
            // 获取正确的bucketName参数
            let bucketName = null;
            if (repoIds.value) {
                // 如果有选择知识库，查找对应的知识库信息
                const selectedRepo = repoList.value.find(repo => repo.repoId == repoIds.value);
                if (selectedRepo && selectedRepo.remark) {
                    bucketName = selectedRepo.remark;
                }
            }
            // 如果没有知识库信息或是单位知识库，使用userStore.publicBucketName
            if (!bucketName) {
                bucketName = userStore.publicBucketName;
            }

            console.log('批量删除文件使用的bucketName:', bucketName);
            const response = await RemoveRagFile(params);
            console.log('批量删除文件响应:', response);

            if (response.code === 0) {
                // 从文件列表中移除这些文件
                for (const file of selectedFiles) {
                    const index = fileList.value.findIndex(item => item.id === file.id);
                    if (index !== -1) {
                        fileList.value.splice(index, 1);
                    }
                }

                successCount = fileIds.length;
            } else {
                failCount = fileIds.length;
                console.error('批量删除文件失败:', response.msg || '未知错误');
            }
        } catch (error) {
            console.error('批量删除文件出错:', error);
            failCount = selectedFiles.length;
        }

        // 显示结果消息
        if (successCount > 0 && failCount === 0) {
            showToast(`已成功删除 ${successCount} 个文件`);
        } else if (successCount > 0 && failCount > 0) {
            showToast(`已删除 ${successCount} 个文件，${failCount} 个文件删除失败`, 'warning');
        } else {
            showToast('文件删除失败', 'error');
        }

        // 重置选中状态
        selectAllFiles.value = false;

        // 删除成功后，使用统一方法刷新文件列表
        if (successCount > 0) {
            try {
                console.log('批量删除文件成功，准备刷新文件列表');

                // 延时调用，确保DOM更新完成
                setTimeout(async () => {
                    // 使用统一的加载方法，传入当前知识库ID
                    await loadKnowledgeBaseFiles(repoIds.value);

                    console.log('文件列表刷新完成');
                }, 300); // 延长时间确保DOM完全更新
            } catch (error) {
                console.error('刷新文件列表失败:', error);
            }
        }
    }).catch(() => {
        // 用户取消删除
    });
};

// 获取文件图标的函数
const getFileIconByExtension = (filename) => {
    if (!filename) return 'mdi-file';

    // 强制确保文件名是字符串类型
    const filenameStr = String(filename);

    // 检查文件名是否包含.pdf（不区分大小写）
    if (filenameStr.toLowerCase().endsWith('.pdf')) {
        return 'mdi-file-pdf-box'; // 使用更明显的PDF图标
    }

    // 获取文件扩展名
    const parts = filenameStr.split('.');
    const extension = parts.length > 1 ? parts.pop().toLowerCase() : '';

    // 根据文件扩展名返回对应的图标
    switch (extension) {
        // 文档类型
        case 'doc':
        case 'docx':
            return 'mdi-file-word';
        case 'xls':
        case 'xlsx':
            return 'mdi-file-excel';
        case 'ppt':
        case 'pptx':
            return 'mdi-file-powerpoint';
        case 'pdf':
            return 'mdi-file-pdf-box'; // 使用更明显的PDF图标

        // 图片类型
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
        case 'svg':
            return 'mdi-file-image';

        // 音频视频类型
        case 'mp3':
        case 'wav':
        case 'ogg':
            return 'mdi-file-music';
        case 'mp4':
        case 'avi':
        case 'mov':
        case 'wmv':
            return 'mdi-file-video';

        // 压缩文件类型
        case 'zip':
        case 'rar':
        case '7z':
        case 'tar':
        case 'gz':
            return 'mdi-zip-box';

        // 代码和文本类型
        case 'txt':
            return 'mdi-file-document';
        case 'html':
        case 'xml':
            return 'mdi-language-html5';
        case 'css':
            return 'mdi-language-css3';
        case 'js':
        case 'json':
            return 'mdi-language-javascript';
        case 'py':
            return 'mdi-language-python';
        case 'java':
            return 'mdi-language-java';
        case 'c':
        case 'cpp':
            return 'mdi-language-cpp';

        // 默认图标
        default:
            return 'mdi-file';
    }
};

// 获取文件图标颜色
const getFileIconColor = (filename) => {
    if (!filename) return 'grey';

    // 强制确保文件名是字符串类型
    const filenameStr = String(filename);

    // 检查文件名是否包含.pdf（不区分大小写）
    if (filenameStr.toLowerCase().endsWith('.pdf')) {
        return 'red-darken-2'; // PDF使用红色
    }

    // 获取文件扩展名
    const parts = filenameStr.split('.');
    const extension = parts.length > 1 ? parts.pop().toLowerCase() : '';

    // 根据文件扩展名返回对应的颜色
    switch (extension) {
        // 文档类型
        case 'doc':
        case 'docx':
            return 'blue';
        case 'xls':
        case 'xlsx':
            return 'green';
        case 'ppt':
        case 'pptx':
            return 'orange';
        case 'pdf':
            return 'red-darken-2'; // 使用更深的红色使PDF更明显

        // 图片类型
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
        case 'svg':
            return 'purple';

        // 音频视频类型
        case 'mp3':
        case 'wav':
        case 'ogg':
            return 'pink';
        case 'mp4':
        case 'avi':
        case 'mov':
        case 'wmv':
            return 'deep-purple';

        // 压缩文件类型
        case 'zip':
        case 'rar':
        case '7z':
        case 'tar':
        case 'gz':
            return 'amber';

        // 默认颜色
        default:
            return 'grey-darken-1';
    }
};

// 左侧边栏控制
const isLeftSidebarOpen = ref(false);
const activeTab = ref('scenarios'); // 默认显示场景标签页

// 成员管理抽屉相关
const memberDrawerOpen = ref(false);
const memberSearchQuery = ref('');
const memberStatusFilter = ref('全部');
const statusOptions = ['全部', '正常', '禁用'];
const selectAllMembers = ref(false);
const pageSize = ref(10);
const currentPage = ref(1);
const goToPage = ref('1');
const totalMembersCount = ref(0); // 总成员数
const isLoading = ref(false); // 加载状态（重命名以避免冲突）
const currentRepoId = ref(null); // 当前知识库ID

// 成员数据
const members = ref([]);

// 过滤后的成员列表
const filteredMembers = computed(() => {
    // 返回当前页的数据，分页由后端处理
    return members.value;
});

// 添加左侧知识库列表ref变量
const repoList = ref([]);

const getLeftList = async () => {
    let params = {
        pageNum: 1,
        pageSize: 100
    }
    const res = await reposList(params);
    console.log('左侧列表:', res);
    if (res.code == 0) {
        repoList.value = res.data.list;
        console.log('知识库列表已加载，共', repoList.value.length, '个知识库');

        // 打印知识库列表的详细数据
        console.log('知识库列表完整数据:', repoList.value);

        // 打印每个知识库的详细属性
        repoList.value.forEach((repo, index) => {
            console.log(`知识库 ${index + 1} (${repo.repoDesc}) 详细数据:`, {
                repoId: repo.repoId,
                repoDesc: repo.repoDesc,
                remark: repo.remark,
                isJoined: repo.isJoined,
                operationPermission: repo.operationPermission,
                createTime: repo.createTime,
                updateTime: repo.updateTime,
                createBy: repo.createBy,
                updateBy: repo.updateBy,
                其他属性: repo
            });
        });
    }
}

// 获取成员列表
const fetchMembersList = async () => {
    isLoading.value = true;
    try {
        // 查找当前知识库的ID
        if (!currentRepoId.value) {
            const repo = repoList.value.find(repo => repo.repoDesc === currentKnowledgeBase.value);
            if (repo) {
                currentRepoId.value = repo.repoId;
            }
        }

        // 构建请求参数
        const params = {
            pageNum: currentPage.value,
            pageSize: pageSize.value,
            repoId: currentRepoId.value,
            repositoryId: currentRepoId.value
        };

        // 如果有筛选条件，添加到参数中
        if (memberStatusFilter.value !== '全部') {
            params.permissionType = memberStatusFilter.value === '管理员' ? 1 : 2;
        }

        // 如果有搜索关键词，添加到参数中
        if (memberSearchQuery.value) {
            params.searchKey = memberSearchQuery.value;
        }

        const res = await MembersList(params);
        console.log('成员列表:', res);
        if (res.code == 0) {
            // 转换数据格式
            members.value = res.data.list.map(member => ({
                id: member.id,
                name: member.nickName || member.userName,
                status: member.userStatus == '0' ? '正常' : '禁用',
                phone: member.phonenumber || '-',
                joinDate: member.createTime || '-',
                role: member.operationPermission == 'EDIT' ? '管理员' : '成员',
                avatarColor: generateColorFromName(member.nickName || member.userName),
                selected: false,
                userId: member.userId
            }));
            totalMembersCount.value = res.data.total;
        }
        if (res.code == -1) {
            members.value = [];
            totalMembersCount.value = 0;
        }
    } catch (error) {
        members.value = [];
        totalMembersCount.value = 0;
        console.error('获取成员列表失败:', error);
        // showToast('获取成员列表失败', 'error');
    } finally {
        isLoading.value = false;
    }
};

// 根据名称生成随机但固定的颜色
const generateColorFromName = (name) => {
    const colors = ['purple', 'cyan', 'orange', 'teal', 'blue', 'pink', 'red', 'green', 'blue-grey', 'deep-purple'];
    const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
};

// 打开成员管理抽屉
const openMemberDrawer = (knowledgeBaseName) => {
    currentKnowledgeBase.value = knowledgeBaseName || '全部知识库';

    // 重置分页和搜索条件
    currentPage.value = 1;
    memberSearchQuery.value = '';
    memberStatusFilter.value = '全部';

    // 打开抽屉
    memberDrawerOpen.value = true;

    // 查找当前知识库的ID
    const repo = repoList.value.find(repo => repo.repoDesc == currentKnowledgeBase.value);
    console.log(repo);

    if (repo) {
        currentRepoId.value = repo.repoId;
    } else {
        // 如果找不到对应的知识库，设为null
        currentRepoId.value = null;
    }

    // 加载成员列表
    fetchMembersList();
};

// 添加新成员
const addNewMember = () => {
    // 重置搜索和表单
    resetUserSearch();
    newUser.value.remark = '';

    // 获取用户列表
    fetchUserSelectOptions();

    // 显示对话框
    showAddUserDialog.value = true;
};

// 处理页码变化
const handlePageChange = (page) => {
    currentPage.value = page;
    fetchMembersList();
};

// 处理每页条数变化
const handlePageSizeChange = (size) => {
    pageSize.value = size;
    currentPage.value = 1; // 切换每页条数时，重置为第一页
    fetchMembersList();
};

// 前一页
const handlePrevPage = () => {
    if (currentPage.value > 1) {
        currentPage.value--;
        fetchMembersList();
    }
};

// 后一页
const handleNextPage = () => {
    if (currentPage.value < Math.ceil(totalMembersCount.value / pageSize.value)) {
        currentPage.value++;
        fetchMembersList();
    }
};

// 跳转到指定页
const navigateToPage = () => {
    const page = parseInt(goToPage.value);
    if (page && page > 0 && page <= Math.ceil(totalMembersCount.value / pageSize.value)) {
        currentPage.value = page;
        fetchMembersList();
    }
};

// 监听全选框变化
watch(selectAllMembers, (newVal) => {
    members.value.forEach(member => {
        member.selected = newVal;
    });
});

// 监听成员选择状态变化
watch(members, () => {
    // 检查是否所有成员都被选中
    const allSelected = members.value.every(member => member.selected);
    const someSelected = members.value.some(member => member.selected);

    // 更新全选框状态
    selectAllMembers.value = allSelected ? true : someSelected ? null : false;
}, { deep: true });

const toggleLeftSidebar = () => {
    isLeftSidebarOpen.value = !isLeftSidebarOpen.value;
};

// 知识库相关
const showFileDetails = () => {
    // 模拟点击文件项的行为
    showToast('查看文件详情', 'info');
};

const createKnowledgeBase = () => {
    // 模拟创建知识库的行为
    showToast('创建知识库功能开发中', 'info');
};


// axios 请求头配置
const axiosHeaders = {
    'Authorization': getApiToken(),
    'Content-Type': 'application/json',

};

const userMessage = ref('');
const sessionId = ref('');
const messages = ref([]);
const chatContainer = ref(null);
const isProcessing = ref(false);
const showBackToTopBtn = ref(false); // 控制返回顶部按钮显示
const isThinkingVisible = ref(true); // 控制思考内容始终可见

// 滚动到文件列表区域（返回顶部）
const scrollToFileList = () => {
    // 平滑滚动到页面顶部
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
};

// 监听滚动事件，控制返回顶部按钮显示
const handleScroll = () => {
    if (window.scrollY > 500) {
        showBackToTopBtn.value = true;
    } else {
        showBackToTopBtn.value = false;
    }
};

const showClearConfirmDialog = ref(false);
const showClearHistoryConfirmDialog = ref(false);
const chatHistory = ref({
    recent7Days: [],
    recent30Days: [],
    before30Days: []
});
const selectedHistoryIndex = ref(-1);
const latestReferenceChunks = ref([]);
const latestDocAggs = ref([]);
const typingCompleted = ref(false);
const historyLoadAttempted = ref(false); // 标记是否已尝试加载历史记录
const toast = ref({
    show: false,
    message: '',
    type: 'success', // 'success' or 'info'
    timeout: null
});

// 模式选择器
const selectedMode = ref({
    icon: 'mdi-brain',
    text: '日常问答',
    value: 'r1'
});

// 联网状态 - 只在内容创作模式下使用
const isNetworkEnabled = ref(false);

// 内容转义函数 - 转义HTML标签以避免数据库存储问题
const escapeHtmlContent = (content) => {
    try {
        // 转义HTML标签，特别是<a>标签和其他可能导致数据库问题的字符
        return content
            .replace(/&/g, '&amp;')     // 先转义&符号
            .replace(/</g, '&lt;')      // 转义<符号
            .replace(/>/g, '&gt;')      // 转义>符号
            .replace(/"/g, '&quot;')    // 转义双引号
            .replace(/'/g, '&#39;');    // 转义单引号
    } catch (error) {
        console.error('内容转义失败，使用原始内容:', error);
        return content;
    }
};

// 内容反转义函数 - 将转义的HTML实体还原为原始字符
const unescapeHtmlContent = (content) => {
    try {
        // 反转义HTML实体，顺序与转义时相反
        return content
            .replace(/&#39;/g, "'")     // 反转义单引号
            .replace(/&quot;/g, '"')    // 反转义双引号
            .replace(/&gt;/g, '>')      // 反转义>符号
            .replace(/&lt;/g, '<')      // 反转义<符号
            .replace(/&amp;/g, '&');    // 最后反转义&符号
    } catch (error) {
        console.error('内容反转义失败，使用原始内容:', error);
        return content;
    }
};

const modeOptions = [
    {
        icon: 'mdi-brain',
        text: '日常问答',
        value: 'r1'
    },
    {
        icon: 'mdi-thought-bubble',
        text: 'i暖城问数',
        value: 'v2' // 保持 v2 作为值，与其他地方的判断一致
    },
    {
        icon: 'mdi-pencil-outline',
        text: '内容创作',
        value: 'content_creation'
    },
    {
        icon: 'mdi-gavel',
        text: '政策问答',
        value: 'policy_qa'
    },
    {
        icon: 'mdi-file-document-edit',
        text: '创城工作报告',
        value: 'WORK_REPORT'
    }
];

// 打印模式选项以便调试
console.log('模式选项:', modeOptions);

// 响应式设计相关
const isMobile = ref(false);
const isLandscape = ref(false);

// 欢迎消息 - 仅包含结构和建议问题，内容将由API响应填充
const welcomeMessage = {
    type: 'system',
    welcome: true,
    content: '', // 内容将从API响应中获取
    typingCompleted: true,
    showReferences: true,
    suggestedQuestions: [
        '累计发现问题数',
        '今日发现问题数',
        '已整改问题数',
        '正在解决问题数',
        '全部问题整改率',
        '现状问题状态分布',
        '旗区工作量统计',
        '东康问题总数',
        '近三个月东康问题总数'
    ]
};

// 创建新会话
const createNewSession = async (showSuccessToast = false) => {
    // 添加详细调试日志
    console.log('createNewSession 被调用，showSuccessToast=', showSuccessToast);

    // 检查当前是否在查看历史记录，并且历史记录是否已全部清空
    const isHistoryEmpty =
        chatHistory.value.recent7Days.length === 0 &&
        chatHistory.value.recent30Days.length === 0 &&
        chatHistory.value.before30Days.length === 0;

    const wasViewingHistory = selectedHistoryIndex.value !== -1;

    console.log('历史记录是否为空:', isHistoryEmpty);
    console.log('是否在查看历史记录:', wasViewingHistory);

    // 如果历史记录已清空且正在查看历史记录，重置查看状态
    if (isHistoryEmpty && wasViewingHistory) {
        console.log('历史记录已全部清空且正在查看历史记录，将重置对话');
    }

    // 重置历史记录索引，表示不再查看历史记录
    selectedHistoryIndex.value = -1;

    console.log('完整消息列表:', JSON.stringify(messages.value));

    // 检查是否有用户消息和机器人回复
    const userMessages = messages.value.filter(msg => msg.type === 'user');
    const assistantMessages = messages.value.filter(msg => msg.type === 'system' && !msg.welcome);
    console.log('用户消息数量:', userMessages.length);
    console.log('机器人回复数量:', assistantMessages.length);
    console.log('用户消息列表:', userMessages.map(msg => ({ content: msg.content })));
    console.log('机器人回复列表:', assistantMessages.map(msg => ({ content: msg.content.substring(0, 50) + '...' })));
    console.log('当前知识库ID (repoIds.value):', repoIds.value);

    // 检查是否有对话内容
    const hasUserMessages = messages.value.some(msg => msg.type === 'user');
    console.log('是否有用户消息:', hasUserMessages);

    // 打印更多详细数据
    console.log('当前会话ID:', sessionId.value);
    console.log('当前模式:', selectedMode.value);
    console.log('当前历史记录数量:', Object.keys(chatHistory.value).length);
    console.log('当前选中的历史记录索引:', selectedHistoryIndex.value);
    console.log('当前参考文献:', latestReferenceChunks.value);
    console.log('当前API基础URL:', getApiBaseUrl());
    console.log('当前APIgetApiToken:', getApiToken());
    console.log('当前CHAT_ID:', CHAT_ID);
    console.log('当前AGENT_ID:', AGENT_ID);

    // 如果当前有对话内容且不是在查看历史记录，先保存到历史记录
    if (messages.value.length > 1 && !wasViewingHistory && !isHistoryEmpty) {
        try {
            await saveCurrentConversation();
            console.log('已保存当前对话到历史记录');
        } catch (error) {
            console.error('保存当前对话失败:', error);
        }
    }

    // 重置选中的历史记录索引
    selectedHistoryIndex.value = -1;

    // 清除localStorage中保存的会话ID
    localStorage.removeItem('current_conversation_id');
    console.log('已清除会话ID');

    // 重置会话ID，强制创建新会话
    sessionId.value = '';
    console.log('已重置会话ID');

    // 清空当前对话内容
    messages.value = [];
    console.log('已清空当前对话内容');

    // 重置参考文献和参考文档数据
    latestReferenceChunks.value = [];
    latestDocAggs.value = [];
    console.log('已重置参考文献和参考文档数据');

    // 重置模式为日常问答
    selectedMode.value = modeOptions.find(mode => mode.value === 'r1') || modeOptions[0];
    console.log('已重置模式为日常问答:', selectedMode.value);

    // 显示处理中状态
    isProcessing.value = true;

    try {
        // 调用创建会话接口
        console.log('正在调用创建会话接口...');
        const result = await createSession();

        if (result) {
            // 只有在明确要求显示成功提示时才显示
            if (showSuccessToast) {
                showToast('已创建新会话');
            }
        } else {
            showToast('创建新会话失败', 'error');
            console.error('创建会话接口返回失败');
        }
    } catch (error) {
        console.error('创建新会话出错:', error);
        showToast('创建新会话失败', 'error');
    } finally {
        isProcessing.value = false;
    }
};

// 创建会话
const createSession = async (clearMessages = true) => {
    // 在函数开始时记录调用
    console.log(`开始创建会话... ${clearMessages ? '(将清除消息列表)' : '(保留消息列表)'}`);
    try {
        // 获取当前选择的知识库ID
        let kbId = null;
        let repoId = null;

        // 首先尝试从左侧菜单选择的知识库获取ID
        if (repoIds.value) {
            repoId = repoIds.value;

            // 查找对应的知识库详细信息
            const selectedRepo = repoList.value.find(repo => repo.repoId == repoIds.value);

            if (selectedRepo) {
                // 根据isJoined状态决定使用哪个ID作为kbId
                if (selectedRepo.isJoined) {
                    kbId = selectedRepo.repoId;
                    console.log('创建会话：已加入的知识库，使用repoId作为kbId:', kbId);
                } else {
                    kbId = selectedRepo.sharedRepoId || selectedRepo.repoId;
                    console.log('创建会话：未加入的知识库，使用sharedRepoId作为kbId:', kbId);
                }
            } else {
                kbId = repoIds.value;
                console.log('创建会话：未找到知识库详情，使用repoIds作为kbId:', kbId);
            }
        }
        // 如果左侧没有选择，则从下拉选择框获取
        else if (selectedKnowledgeBase.value) {
            if (selectedKnowledgeBase.value.value === 'unit') {
                // 单位知识库，使用null
                kbId = null;
                repoId = null;
                console.log('创建会话：单位知识库，使用null作为kbId和repoId');
            } else if (selectedKnowledgeBase.value.value.startsWith('repo_')) {
                // 特定知识库，提取ID
                const selectedRepoId = selectedKnowledgeBase.value.value.split('_')[1];
                repoId = selectedRepoId;

                // 查找对应的知识库详细信息
                const selectedRepo = repoList.value.find(repo => repo.repoId == selectedRepoId);

                if (selectedRepo) {
                    // 根据isJoined状态决定使用哪个ID作为kbId
                    if (selectedRepo.isJoined) {
                        kbId = selectedRepo.repoId;
                        console.log('创建会话：已加入的知识库，使用repoId作为kbId:', kbId);
                    } else {
                        kbId = selectedRepo.sharedRepoId || selectedRepo.repoId;
                        console.log('创建会话：未加入的知识库，使用sharedRepoId作为kbId:', kbId);
                    }
                } else {
                    kbId = selectedRepoId;
                    console.log('创建会话：未找到知识库详情，使用selectedRepoId作为kbId:', kbId);
                }
            }
        }

        console.log('创建会话，选择的知识库ID (kbId):', kbId);
        console.log('创建会话，选择的知识库ID (repoId):', repoId);

        // 准备发送的数据
        let requestData = {
            "name": "新对话",
            "role": "user", // 添加角色参数
            "env": selectedMode.value.value === 'WORK_REPORT' ? ENV_BG : ENV // 创城工作报告模式使用ENV_BG
        };

        // 处理kbId参数
        if (selectedMode.value.value === 'WORK_REPORT') {
            // 创城工作报告模式使用null
            requestData.kbId = null;
            // 添加deptId参数
            requestData.deptId = userStore.users?.deptId || localStorage.getItem('deptId');
            console.log('创城工作报告模式，使用kbId: null, deptId:', requestData.deptId);
        } else if (selectedMode.value.value === 'content_creation') {
            // 内容创作模式添加type参数，根据联网状态决定
            requestData.type = isNetworkEnabled.value ? 'intel' : 'local';
            if (kbId !== null) {
                requestData.kbId = String(kbId); // 确保 kbId 是字符串类型
            } else {
                requestData.kbId = null;
            }
            console.log('内容创作模式，使用kbId:', requestData.kbId, 'type:', requestData.type);
        } else if (kbId !== null) {
            requestData.kbId = String(kbId); // 确保 kbId 是字符串类型
        } else {
            // 单位知识库时，设置kbId为null
            requestData.kbId = null; // 单位知识库时，使用null
            console.log('单位知识库模式，设置kbId为null');
        }

        // 注意：不再添加 repoId 参数，因为API不需要

        // 将对象转换为 JSON 字符串
        const data = JSON.stringify(requestData);

        // 根据当前选择的模式决定使用哪个API端点
        let agentId;
        switch (selectedMode.value.value) {
            case 'v2':
                agentId = AGENT_ID;
                break;
            case 'content_creation':
                agentId = AGENT_ID_NR;
                break;
            case 'policy_qa':
                agentId = AGENT_ID_ZC;
                break;
            case 'WORK_REPORT':
                agentId = AGENT_ID_BG;
                break;
            case 'r1':
            default:
                agentId = CHAT_ID;
                break;
        }
        const url = `${getApiBaseUrl()}/api/v1/agents/${agentId}/sessions`;

        // 打印详细的会话创建请求信息
        console.log('创建会话请求详情:', {
            url: url,
            mode: selectedMode.value.value,
            agentId: agentId,
            data: data,
            kbId: requestData.kbId
        });

        const config = {
            method: 'post',
            url: url,
            headers: {
                'Authorization': getApiToken(),
                'Content-Type': 'application/json',
            },
            data: data,
            timeout: 10000 // 设置10秒超时，避免长时间等待
        };

        // 发起请求并获取响应
        const response = await axios(config);

        console.log('创建会话响应:', response.data);

        if (response.status !== 200) {
            throw new Error(`创建会话失败: ${response.status}`);
        }

        // 特别检查单位知识库模式下的响应
        if (kbId === null || requestData.kbId === null) {
            console.log('单位知识库模式创建会话响应:', response.data);
        }

        // 根据当前模式提取会话ID
        if (selectedMode.value.value == 'v2') {
            // 问数BOT模式的会话ID结构
            sessionId.value = response.data.data?.id || '';
            console.log('问数BOT会话ID:', sessionId.value);
        } else {
            // 问答BOT模式的会话ID结构
            sessionId.value = response.data.data?.id || '';
            console.log('问答BOT会话ID:', sessionId.value);
        }

        // 检查会话ID是否成功设置
        if (!sessionId.value) {
            console.error('创建会话后未能获取有效的会话ID，response:', response.data);
            throw new Error('创建会话后未能获取有效的会话ID');
        }

        // 创建欢迎消息的基础结构
        const baseWelcomeMsg = {
            type: 'system',
            welcome: true,
            suggestedQuestions: welcomeMessage.suggestedQuestions,
            typingCompleted: true,
            showReferences: true
        };
        console.log('API响应:', response.data);

        // 根据参数决定是否清除消息列表
        if (clearMessages) {
            console.log('清除消息列表');
            messages.value = [];
        } else {
            console.log('保留现有消息列表');
        }

        // 根据不同模式获取正确的欢迎消息
        try {
            // 打印完整的response数据，用于调试
            console.log('完整的欢迎消息响应数据:', JSON.stringify(response.data));

            if (selectedMode.value.value == 'v2') {
                // 问数BOT使用 message 结构
                if (response.data.data && response.data.data.message && response.data.data.message.length > 0) {
                    // 使用第一条消息的content作为欢迎消息
                    baseWelcomeMsg.content = response.data.data.message[0].content || '';
                    console.log('问数BOT欢迎消息:', baseWelcomeMsg.content);
                } else {
                    baseWelcomeMsg.content = '您好，我是数据查询助手，可以帮您查询各类数据。';
                    console.log('问数BOT未找到欢迎消息，使用默认消息');
                }
            } else {
                // 尝试不同的可能路径获取欢迎消息
                let welcomeContent = '';

                // 尝试方式1: data.data.messages[0].content
                if (response.data.data && response.data.data.messages && response.data.data.messages.length > 0) {
                    welcomeContent = response.data.data.messages[0].content || '';
                }
                // 尝试方式2: data.data.message[0].content
                else if (response.data.data && response.data.data.message && response.data.data.message.length > 0) {
                    welcomeContent = response.data.data.message[0].content || '';
                }
                // 尝试方式3: data.messages[0].content
                else if (response.data.messages && response.data.messages.length > 0) {
                    welcomeContent = response.data.messages[0].content || '';
                }
                // 尝试方式4: data.message[0].content
                else if (response.data.message && response.data.message.length > 0) {
                    welcomeContent = response.data.message[0].content || '';
                }

                if (welcomeContent) {
                    baseWelcomeMsg.content = welcomeContent;
                    console.log('问答BOT欢迎消息成功获取:', baseWelcomeMsg.content);
                } else {
                    baseWelcomeMsg.content = '您好，我是智能助手，可以回答您关于知识库的问题。';
                    console.log('问答BOT未找到欢迎消息，使用默认消息');
                }
            }
        } catch (error) {
            console.error('获取欢迎消息失败:', error);
            // 错误时使用默认消息
            baseWelcomeMsg.content = '您好，我是您的智能助手，可以回答您关于中心知识库的相关问题。';
        }

        // 再次确认会话ID是否存在且有效
        console.log('最终确认会话ID:', sessionId.value);

        // 根据参数决定是否设置欢迎消息
        if (clearMessages) {
            // 设置欢迎消息
            messages.value = [baseWelcomeMsg];

            // 如果是移动设备，调整建议问题
            if (isMobile.value) {
                adaptSuggestedQuestions();
            }
        } else {
            console.log('保留现有消息列表，不添加欢迎消息');
        }

        return true;
    } catch (error) {
        console.error('创建会话失败:', error);
        // showToast('会话创建失败，请刷新页面重试', 'error');
        return false;
    }
};

// 发送消息并处理流式响应
const sendMessageToAPI = async (question, isSuggestedQuestion = false) => {
    console.log(`发送消息到API${isSuggestedQuestion ? '(快捷词)' : ''}:`, question);

    // 重置参考文献和参考文档数据，避免上次的数据被错误保存
    if (!isSuggestedQuestion) {
        latestReferenceChunks.value = [];
        latestDocAggs.value = [];
        console.log('已重置参考文献和参考文档数据');
    }

    // 检查会话ID是否存在
    if (!sessionId.value) {
        console.error('发送消息失败：会话ID不存在，当前sessionId:', sessionId.value);
        showToast('会话ID不存在，请重新开始对话', 'error');
        return false;
    }

    try {
        // 确保用户消息仍然存在
        // 在历史记录模式下，question可能是拼接后的消息，我们需要检查是否已经有用户消息
        const userMessages = messages.value.filter(msg => msg.type === 'user');

        // 只有在没有任何用户消息时才添加，避免显示拼接后的消息
        if (userMessages.length == 0) {
            // 尝试提取原始用户输入（如果是拼接的消息，只取最后一行）
            const userInput = question.split('\n').pop() || question;
            console.log('在发送消息前用户消息丢失，重新添加用户消息:', userInput);
            messages.value.push({
                type: 'user',
                content: userInput,
                typingCompleted: true, // 用户消息总是完成的
                responseCompleted: true // 用户消息总是完成的
            });
        }

        // 添加系统思考状态消息，并记录开始时间
        // 如果是快捷词且已经有思考中状态消息，则不再添加新的思考消息
        let thinkingMessageIndex = messages.value.length - 1;
        let thinkingStartTime = Date.now();

        // 检查最后一条消息是否已经是思考中状态
        const lastMessage = messages.value[thinkingMessageIndex];
        const isLastMessageThinking = lastMessage && lastMessage.thinking;

        if (!isSuggestedQuestion || !isLastMessageThinking) {
            // 如果不是快捷词，或者最后一条消息不是思考中状态，则添加新的思考消息
            console.log('添加新的思考中状态消息');
            thinkingMessageIndex = messages.value.length;
            thinkingStartTime = Date.now();
            console.log('记录思考开始时间:', thinkingStartTime);
            messages.value.push({
                type: 'system',
                content: '', // 初始内容为空，将在流式响应中更新
                thinking: false, // 不显示"思考中..."，直接显示接口返回的内容
                typingCompleted: false,
                showReferences: false,
                thinkingTime: thinkingStartTime, // 记录开始思考的时间
                preserveThinking: true // 确保思考步骤被保留
            });
        } else {
            console.log('使用已有的思考中状态消息');
        }

        // 获取当前选择的知识库ID
        let kbId = null;
        let repoId = null;

        // 首先尝试从左侧菜单选择的知识库获取ID
        if (repoIds.value) {
            repoId = repoIds.value;

            // 查找对应的知识库详细信息
            const selectedRepo = repoList.value.find(repo => repo.repoId == repoIds.value);

            if (selectedRepo) {
                // 根据isJoined状态决定使用哪个ID作为kbId
                if (selectedRepo.isJoined) {
                    kbId = selectedRepo.repoId;
                    console.log('已加入的知识库，使用repoId作为kbId:', kbId);
                } else {
                    kbId = selectedRepo.sharedRepoId || selectedRepo.repoId;
                    console.log('未加入的知识库，使用sharedRepoId作为kbId:', kbId);
                }
            } else {
                kbId = repoIds.value;
                console.log('未找到知识库详情，使用repoIds作为kbId:', kbId);
            }
        }
        // 如果左侧没有选择，则从下拉选择框获取
        else if (selectedKnowledgeBase.value) {
            if (selectedKnowledgeBase.value.value === 'unit') {
                // 单位知识库，使用null
                kbId = null;
                repoId = null;
                console.log('单位知识库，使用null作为kbId和repoId');
            } else if (selectedKnowledgeBase.value.value.startsWith('repo_')) {
                // 特定知识库，提取ID
                const selectedRepoId = selectedKnowledgeBase.value.value.split('_')[1];
                repoId = selectedRepoId;

                // 查找对应的知识库详细信息
                const selectedRepo = repoList.value.find(repo => repo.repoId == selectedRepoId);

                if (selectedRepo) {
                    // 根据isJoined状态决定使用哪个ID作为kbId
                    if (selectedRepo.isJoined) {
                        kbId = selectedRepo.repoId;
                        console.log('已加入的知识库，使用repoId作为kbId:', kbId);
                    } else {
                        kbId = selectedRepo.sharedRepoId || selectedRepo.repoId;
                        console.log('未加入的知识库，使用sharedRepoId作为kbId:', kbId);
                    }
                } else {
                    kbId = selectedRepoId;
                    console.log('未找到知识库详情，使用selectedRepoId作为kbId:', kbId);
                }
            }
        }

        // 打印所有相关的知识库选择信息
        console.log('发送消息时的知识库选择数据:', {
            kbId: kbId,
            repoId: repoId,
            repoIds: repoIds.value,
            selectedKnowledgeBase: selectedKnowledgeBase.value,
            knowledgeBaseOptions: knowledgeBaseOptions.value,
            userPublicBucketName: userStore.publicBucketName
        });

        console.log('发送消息，使用知识库ID (kbId):', kbId);
        console.log('发送消息，使用知识库ID (repoId):', repoId);
        console.log('发送消息，使用会话ID:', sessionId.value);

        // 检查sessionId是否有效，这是一个安全措施
        if (!sessionId.value) {
            console.error('严重错误：发送请求前sessionId为空');
            // showToast('会话ID无效，请刷新页面重试', 'error');
            return false;
        }

        // 准备发送的数据 - 一定要使用sessionId.value，不要使用localStorage的会话ID
        // sessionId.value是sessions接口返回的会话ID，这是我们应该始终使用的
        let requestData = {
            question: question,
            stream: true,
            session_id: sessionId.value, // 使用sessions接口返回的sessionId
            role: "user", // 添加角色参数
            env: selectedMode.value.value === 'WORK_REPORT' ? ENV_BG : ENV // 创城工作报告模式使用ENV_BG
        };

        // 处理kbId参数
        if (selectedMode.value.value === 'WORK_REPORT') {
            // 创城工作报告模式使用null
            requestData.kbId = null;
            // 添加deptId参数
            requestData.deptId = userStore.users?.deptId || localStorage.getItem('deptId');
            console.log('创城工作报告模式，使用kbId: null, deptId:', requestData.deptId);
        } else if (selectedMode.value.value === 'content_creation') {
            // 内容创作模式添加type参数，根据联网状态决定
            requestData.type = isNetworkEnabled.value ? 'intel' : 'local';
            if (kbId !== null) {
                requestData.kbId = String(kbId); // 确保 kbId 是字符串类型
            } else {
                requestData.kbId = null;
            }
            console.log('内容创作模式，使用kbId:', requestData.kbId, 'type:', requestData.type);
        } else if (kbId !== null) {
            requestData.kbId = String(kbId); // 确保 kbId 是字符串类型
        } else {
            // 单位知识库时，设置kbId为null
            requestData.kbId = null; // 单位知识库时，使用null
            console.log('单位知识库模式，设置kbId为null');
        }

        // 注意：不再添加 repoId 参数，因为API不需要

        // 将对象转换为 JSON 字符串
        const data = JSON.stringify(requestData);

        console.log('API请求数据:', data);

        // 处理流式响应
        const controller = new AbortController();
        activeController.value = controller; // 保存当前控制器引用
        const signal = controller.signal;

        // 创建变量来保存累积的响应文本和最新回答
        let accumulatedText = '';
        let latestAnswer = null;

        // 根据当前模式选择正确的API端点
        let agentId;
        switch (selectedMode.value.value) {
            case 'v2':
                agentId = AGENT_ID;
                break;
            case 'content_creation':
                agentId = AGENT_ID_NR;
                break;
            case 'policy_qa':
                agentId = AGENT_ID_ZC;
                break;
            case 'WORK_REPORT':
                agentId = AGENT_ID_BG;
                break;
            case 'r1':
            default:
                agentId = CHAT_ID;
                break;
        }
        const apiUrl = `${getApiBaseUrl()}/api/v1/agents/${agentId}/completions`;

        console.log('API端点URL:', apiUrl);
        console.log('当前模式:', selectedMode.value.value);
        console.log('使用的Agent ID:', agentId);
        console.log('AGENT_ID:', AGENT_ID);
        console.log('AGENT_ID_NR:', AGENT_ID_NR);
        console.log('AGENT_ID_ZC:', AGENT_ID_ZC);
        console.log('CHAT_ID:', CHAT_ID);

        // 使用axios设置
        const config = {
            method: 'post',
            url: apiUrl,
            headers: {
                'Authorization': getApiToken(),
                'Content-Type': 'application/json',
            },
            data: data,
            responseType: 'stream', // 使用JSON模式接收响应
            timeout: 500000, // 设置100秒超时
            onDownloadProgress: (progressEvent) => {
                try {
                    // 获取已接收的数据
                    const response = progressEvent.event.currentTarget.response;

                    // 如果响应存在并且有数据
                    if (response && typeof response == 'string') {
                        // 分割多行SSE数据
                        const lines = response.split('\n').filter(line => line.trim() !== '');

                        // 检查最后一行是否是结束标志
                        const isCompleted = lines.some(line => line == 'data:{"code": 0, "data": true}');

                        // 提取所有有效回答
                        latestAnswer = null; // 重置最新回答
                        let validAnswers = [];

                        // 处理所有行，获取有效回答序列和最新回答
                        for (let i = 0; i < lines.length; i++) {
                            const line = lines[i];
                            if (line.startsWith('data:')) {
                                try {
                                    const jsonStr = line.substring(5); // 去掉"data:"前缀
                                    const jsonData = JSON.parse(jsonStr);

                                    // 打印完整的响应数据，以便调试单位知识库响应问题
                                    console.log('接收到的API响应数据:', jsonData);

                                    if (jsonData.data && jsonData.data.answer) {
                                        // 添加到有效回答列表
                                        validAnswers.push(jsonData.data.answer);

                                        // 更新最新回答
                                        latestAnswer = jsonData.data.answer;
                                        console.log('收到回答:', latestAnswer);

                                        // 提取参考文献信息
                                        if (jsonData.data.reference && jsonData.data.reference.chunks) {
                                            latestReferenceChunks.value = jsonData.data.reference.chunks;
                                            // 去重
                                            latestReferenceChunks.value = deduplicateReferences(latestReferenceChunks.value);
                                            console.log('找到参考文献:', latestReferenceChunks.value);
                                        }

                                        // 提取参考文档信息
                                        if (jsonData.data.reference && jsonData.data.reference.doc_aggs) {
                                            latestDocAggs.value = jsonData.data.reference.doc_aggs;
                                            console.log('找到参考文档:', latestDocAggs.value);
                                        }
                                    } else {
                                        console.log('响应数据中没有找到answer字段:', jsonData);
                                    }
                                } catch (e) {
                                    console.error('解析响应数据失败:', e, line);
                                    continue;
                                }
                            }
                        }

                        // 如果找到了有效回答
                        if (latestAnswer) {
                            console.log('处理最新回答:', latestAnswer);
                            try {
                                // 处理回答，完全保留<think>...</think>部分，不做任何替换
                                let processedText = latestAnswer;

                                // 检测是否包含思考部分
                                const hasThinking = processedText.includes('<think>');

                                // 初始化思考块数组
                                const thinkingBlocks = [];

                                // 如果包含思考部分，直接保留原始内容，不做任何处理
                                if (hasThinking) {
                                    console.log('检测到思考文本，直接保留原始内容，不做任何处理');

                                    // 检查是否有未闭合的<think>标签
                                    const hasOpenThink = processedText.lastIndexOf('<think>') > processedText.lastIndexOf('</think>');
                                    console.log('是否有未闭合的<think>标签:', hasOpenThink);

                                    // 不做任何替换，保留原始的<think>标签内容
                                    // 这样流式输出时可以直接显示思考内容
                                }

                                // 使用我们自定义的processContent函数处理内容，而不是marked
                                processedText = processContent(processedText);

                                // 恢复思考块
                                if (hasThinking && thinkingBlocks && thinkingBlocks.length > 0) {
                                    thinkingBlocks.forEach((block, index) => {
                                        // 始终只替换内容，不添加"已完成深度思考"提示
                                        if (block && block.placeholder && block.content) {
                                            processedText = processedText.replace(block.placeholder, block.content);
                                        }
                                    });

                                    // 只有在整个思考过程完成时，才在消息的最后添加"已完成深度思考"提示
                                    if (isCompleted) {
                                        const thinkingTime = messages.value[thinkingMessageIndex]?.thinkingTime;
                                        let hintText = "已完成深度思考";
                                        if (thinkingTime) {
                                            hintText = `已完成深度思考 (用时${thinkingTime}秒)`;
                                        }

                                        // 检查是否还有未处理的<think>标签
                                        const hasOpenThink = processedText.includes('<think>');
                                        const hasAnyThinkingBlock = processedText.includes('class="thinking-block"');

                                        // 只有在没有任何<think>标签和thinking-block时，才显示"已完成深度思考"提示
                                        if (!hasOpenThink && !hasAnyThinkingBlock) {
                                            console.log('思考过程完全结束，添加"已完成深度思考"提示');
                                            // 在最后添加"已完成深度思考"提示
                                            processedText += `<div class="completed-thinking-hint">${hintText}</div>`;
                                        } else {
                                            console.log('思考过程未完全结束，不添加"已完成深度思考"提示');
                                        }
                                    }
                                }

                                // 添加复制和下载功能
                                if (!window.copyCodeToClipboard) {
                                    window.copyCodeToClipboard = function (codeId) {
                                        const codeElement = document.getElementById(codeId);
                                        if (codeElement) {
                                            // 提取原始代码文本（不包含HTML标签）
                                            const codeText = codeElement.textContent;

                                            // 创建一个临时文本区域用于复制
                                            const textarea = document.createElement('textarea');
                                            textarea.value = codeText;
                                            textarea.style.position = 'fixed';
                                            textarea.style.opacity = '0';
                                            document.body.appendChild(textarea);
                                            textarea.select();

                                            try {
                                                // 尝试使用复制命令
                                                const successful = document.execCommand('copy');
                                                if (successful) {
                                                    // 显示复制成功的视觉反馈
                                                    const button = codeElement.closest('.code-block').querySelector('.copy-button');
                                                    const originalText = button.innerHTML;
                                                    button.innerHTML = '<span class="copy-icon">✓</span> 已复制';
                                                    setTimeout(() => {
                                                        button.innerHTML = originalText;
                                                    }, 2000);
                                                } else {
                                                    // 如果execCommand失败，尝试使用clipboard API
                                                    navigator.clipboard.writeText(codeText)
                                                        .then(() => {
                                                            const button = codeElement.closest('.code-block').querySelector('.copy-button');
                                                            const originalText = button.innerHTML;
                                                            button.innerHTML = '<span class="copy-icon">✓</span> 已复制';
                                                            setTimeout(() => {
                                                                button.innerHTML = originalText;
                                                            }, 2000);
                                                        })
                                                        .catch(err => console.error('复制失败:', err));
                                                }
                                            } catch (err) {
                                                console.error('复制过程中出错:', err);
                                            } finally {
                                                // 清理临时元素
                                                document.body.removeChild(textarea);
                                            }
                                        }
                                    };
                                }

                                if (!window.downloadCode) {
                                    window.downloadCode = function (codeId, filename) {
                                        const codeElement = document.getElementById(codeId);
                                        if (codeElement) {
                                            // 提取原始代码文本（不包含HTML标签）
                                            const codeText = codeElement.textContent;
                                            const language = codeElement.getAttribute('data-language');

                                            // 创建Blob对象
                                            const blob = new Blob([codeText], { type: 'text/plain' });

                                            // 创建下载链接
                                            const downloadLink = document.createElement('a');
                                            downloadLink.href = URL.createObjectURL(blob);
                                            downloadLink.download = filename;

                                            // 添加到DOM并触发点击
                                            document.body.appendChild(downloadLink);
                                            downloadLink.click();

                                            // 清理
                                            document.body.removeChild(downloadLink);
                                            URL.revokeObjectURL(downloadLink.href);

                                            // 显示下载成功的视觉反馈
                                            const button = codeElement.closest('.code-block').querySelector('.download-button');
                                            const originalText = button.innerHTML;
                                            button.innerHTML = '<span class="download-icon">✓</span> 已下载';
                                            setTimeout(() => {
                                                button.innerHTML = originalText;
                                            }, 2000);
                                        }
                                    };
                                }

                                // 创建一个专门用于代码功能的全局脚本
                                if (!document.getElementById('code-utils-script')) {
                                    const scriptElement = document.createElement('script');
                                    scriptElement.id = 'code-utils-script';
                                    scriptElement.textContent = `
                    // 该脚本用于确保代码功能在DOM中可访问
                    // 复制和下载功能已移除
                  `;
                                    document.body.appendChild(scriptElement);
                                }

                                accumulatedText = processedText;
                            } catch (e) {
                                // 如果处理失败，直接使用原始文本
                                accumulatedText = latestAnswer;
                                console.error('处理文本失败:', e);
                            }

                            // 针对问数模式(v2)的特殊处理
                            if (selectedMode.value.value == 'v2') {
                                // 如果已完成响应，显示最终答案
                                if (isCompleted) {
                                    // 清除现有的定时器
                                    if (typingInterval) {
                                        clearInterval(typingInterval);
                                    }

                                    console.log('问数模式：显示最终完整答案');
                                    console.log('处理后的内容长度:', accumulatedText.length);
                                    console.log('原始内容长度:', latestAnswer.length);

                                    // 计算思考用时
                                    const thinkingEndTime = Date.now();
                                    const thinkingStartTime = messages.value[thinkingMessageIndex].thinkingStartTime || thinkingEndTime;
                                    const thinkingTime = Math.max(1, Math.round((thinkingEndTime - thinkingStartTime) / 1000)); // 转换为秒，最小为1秒
                                    console.log('思考完成，开始时间:', thinkingStartTime, '结束时间:', thinkingEndTime, '用时:', thinkingTime, '秒');

                                    // 判断是否含有<think>标签，决定是否显示"已完成深度思考"提示
                                    const hasThinkContent = latestAnswer.includes('<think>');
                                    console.log('回答是否包含<think>标签:', hasThinkContent);

                                    // 不显示"已完成深度思考"提示
                                    const showThinkingComplete = false;
                                    console.log('不显示"已完成深度思考"提示');

                                    // 更新消息内容为最终完整答案
                                    console.log('设置最终消息对象，思考时间:', thinkingTime);

                                    // 获取完整的回答内容
                                    const finalContent = accumulatedText || latestAnswer;

                                    // 检查是否包含<think>标签
                                    const hasThinkTag = finalContent.includes('<think>');

                                    // 如果包含<think>标签，从内容中移除<think>标签及其内容
                                    let contentWithoutThink = finalContent;
                                    if (hasThinkTag) {
                                        const thinkContentRegex = /<think>([\s\S]*?)<\/think>/g;
                                        contentWithoutThink = finalContent.replace(thinkContentRegex, '');
                                    }

                                    // 始终使用移除了<think>标签的内容，避免重复显示思考过程
                                    const firstSentences = hasThinkTag ? contentWithoutThink : getFirstSentences(contentWithoutThink, 2);

                                    console.log('是否包含思考标签:', hasThinkTag, '是否截断内容:', !hasThinkTag);

                                    // 设置初始消息对象
                                    messages.value[thinkingMessageIndex] = {
                                        type: 'system',
                                        content: firstSentences, // 使用移除了思考过程的内容
                                        originalContent: latestAnswer, // 保存API返回的原始内容，用于历史记录
                                        fullContent: contentWithoutThink, // 保存移除了思考过程的完整内容用于打字机效果
                                        thinking: false,
                                        thinkingTime: thinkingTime, // 明确设置思考用时
                                        showThinkingComplete: showThinkingComplete, // 是否显示"已完成深度思考"提示
                                        references: latestReferenceChunks.value && latestReferenceChunks.value.length > 0 ? latestReferenceChunks.value : null,
                                        docAggs: latestDocAggs.value && latestDocAggs.value.length > 0 ? latestDocAggs.value : null,
                                        typingCompleted: false, // 始终允许打字机效果，即使有思考内容
                                        responseCompleted: true, // 标记响应已完成
                                        showReferences: true
                                    };

                                    // 开始打字机效果，无论是否有思考内容都进行流式输出
                                    if (!isTypingStarted) {
                                        displayedLength = firstSentences.length; // 从已显示的前几句开始
                                        isTypingStarted = true;

                                        // 清除可能存在的旧定时器
                                        if (typingInterval) {
                                            clearInterval(typingInterval);
                                        }

                                        // 设置打字机效果的速度
                                        const typingSpeed = 20; // 每20毫秒添加一个字符
                                        const increment = 3; // 每次添加3个字符

                                        typingInterval = setInterval(() => {
                                            if (displayedLength < contentWithoutThink.length) {
                                                // 增加显示的字符数
                                                displayedLength = Math.min(displayedLength + increment, contentWithoutThink.length);
                                                const textToShow = contentWithoutThink.substring(0, displayedLength);

                                                // 更新消息内容
                                                messages.value[thinkingMessageIndex].content = textToShow;

                                                // 如果已显示完全部内容，清除定时器并标记为完成
                                                if (displayedLength >= contentWithoutThink.length) {
                                                    clearInterval(typingInterval);
                                                    typingInterval = null;
                                                    isTypingStarted = false;
                                                    displayedLength = 0;

                                                    // 标记打字效果已完成
                                                    messages.value[thinkingMessageIndex].typingCompleted = true;
                                                }
                                            } else {
                                                // 已完成全部内容显示
                                                clearInterval(typingInterval);
                                                typingInterval = null;
                                                isTypingStarted = false;
                                                displayedLength = 0;

                                                // 标记打字效果已完成
                                                messages.value[thinkingMessageIndex].typingCompleted = true;
                                            }
                                        }, typingSpeed);
                                    }

                                    // 滚动到最新消息
                                    // nextTick(() => scrollToBottom());
                                }
                                // 如果还在接收数据，直接显示当前答案
                                else if (validAnswers.length > 0) {
                                    // 获取当前接收到的答案
                                    const currentAnswer = validAnswers[validAnswers.length - 1];

                                    // 处理当前答案
                                    let processedCurrentAnswer = processContent(currentAnswer);

                                    console.log('问数模式：显示实时答案');
                                    console.log('处理后的内容长度:', processedCurrentAnswer.length);
                                    console.log('原始内容长度:', currentAnswer.length);

                                    // 检查是否包含<think>标签
                                    const hasThinkTag = currentAnswer.includes('<think>');

                                    // 如果包含<think>标签，从内容中移除<think>标签及其内容
                                    let contentWithoutThink = processedCurrentAnswer;
                                    if (hasThinkTag) {
                                        const thinkRegexForRemoval = /<think>([\s\S]*?)<\/think>/g;
                                        contentWithoutThink = processedCurrentAnswer.replace(thinkRegexForRemoval, '');
                                    }

                                    // 更新消息显示当前答案，但移除<think>标签内容
                                    const rawContentForDisplay2 = contentWithoutThink || currentAnswer;
                                    const processedContentForDisplay2 = processContent(rawContentForDisplay2);

                                    messages.value[thinkingMessageIndex] = {
                                        type: 'system',
                                        content: processedContentForDisplay2, // 使用经过markdown处理的内容
                                        originalContent: currentAnswer,
                                        thinking: false,
                                        references: null,
                                        typingCompleted: false,
                                        responseCompleted: false, // 明确标记响应未完成
                                        showReferences: false
                                    };

                                    // 不再自动滚动到最新消息
                                    // nextTick(() => scrollToBottom());
                                }
                            }
                            // 非问数模式(r1)的流式输出逻辑
                            else {
                                // 每次收到新文本就直接更新显示，实现真正的流式输出
                                if (latestAnswer) {
                                    // 直接处理内容，<think>标签会被转换为带样式的显示
                                    let mainContent = processContent(latestAnswer);
                                    console.log('流式输出，处理内容并转换<think>标签为带样式显示');

                                    // 直接更新消息内容 - 真正的流式输出
                                    messages.value[thinkingMessageIndex] = {
                                        type: 'system',
                                        content: mainContent, // 使用处理后的内容，<think>标签已转换为带样式的显示
                                        originalContent: latestAnswer, // 保存API返回的原始内容，用于历史记录
                                        thinking: false,
                                        references: latestReferenceChunks.value && latestReferenceChunks.value.length > 0 ? latestReferenceChunks.value : null,
                                        docAggs: latestDocAggs.value && latestDocAggs.value.length > 0 ? latestDocAggs.value : null,
                                        typingCompleted: isCompleted, // 只有在响应完成时才标记为打字完成，允许流式输出
                                        responseCompleted: isCompleted, // 新增标志，表示响应是否完成
                                        showReferences: isCompleted, // 只有在完成响应时才显示参考文献
                                        thinkingSteps: null, // 不再使用专门的思考步骤容器
                                        showThinkingSteps: false,
                                        preserveThinking: false
                                    };

                                    // 滚动到最新消息
                                    // nextTick(() => scrollToBottom());

                                    // 当完成响应时，显示参考文献
                                    if (isCompleted) {
                                        // 先确保思考步骤可见
                                        ensureThinkingStepsVisible();

                                        // 增加延迟时间以确保思考内容完全显示（从100ms增加到3000ms）
                                        // 这个延迟很重要，确保思考部分不会突然消失
                                        setTimeout(() => {
                                            console.log('设置typingCompleted为true');
                                            // 标记打字效果完成
                                            typingCompleted.value = true;
                                            // 再次确保思考可见
                                            isThinkingVisible.value = true;

                                            // 计算思考用时
                                            const thinkingEndTime = Date.now();
                                            const thinkingStartTime = messages.value[thinkingMessageIndex].thinkingStartTime || thinkingEndTime;
                                            const thinkingTime = Math.max(1, Math.round((thinkingEndTime - thinkingStartTime) / 1000)); // 转换为秒，最小为1秒
                                            console.log('思考完成，开始时间:', thinkingStartTime, '结束时间:', thinkingEndTime, '用时:', thinkingTime, '秒');

                                            // 提取思考块内容，确保保存到思考步骤中
                                            const thinkingBlocks = [];
                                            const messageContent = messages.value[thinkingMessageIndex].originalContent || '';
                                            let cleanedContent = messages.value[thinkingMessageIndex].content || '';

                                            // 先检查原始内容中是否有<think>标签，从原始内容中提取
                                            const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
                                            let thinkMatch;
                                            let stepCount = 1;

                                            // 直接从原始内容中提取<think>标签内容
                                            while ((thinkMatch = thinkRegex.exec(messageContent)) !== null) {
                                                // 获取思考内容
                                                let thinkContent = thinkMatch[1];

                                                thinkingBlocks.push({
                                                    step: stepCount++,
                                                    content: thinkContent,
                                                    isLast: false
                                                });
                                            }

                                            // 如果没有从<think>标签中提取到内容，再尝试从thinking-block中提取
                                            if (thinkingBlocks.length === 0) {
                                                // 提取所有thinking-block内容
                                                const thinkingBlockRegex = /<div class="thinking-block[^"]*"[^>]*>([\s\S]*?)<\/div>/g;

                                                while ((thinkMatch = thinkingBlockRegex.exec(messageContent)) !== null) {
                                                    // 获取思考内容
                                                    let thinkContent = thinkMatch[1];

                                                    // 如果内容超过1000字符，截断并添加提示
                                                    if (thinkContent.length > 1000) {
                                                        thinkContent = thinkContent.substring(0, 1000) +
                                                            '<div style="font-style:italic; color:#666; margin-top:8px;">... (内容已截断，显示前1000字符)</div>';
                                                    }

                                                    thinkingBlocks.push({
                                                        step: stepCount++,
                                                        content: thinkContent,
                                                        isLast: false
                                                    });
                                                }
                                            }

                                            // 从正文中移除<think>标签及其内容
                                            const thinkRemovalRegex = /<think>([\s\S]*?)<\/think>/g;
                                            cleanedContent = cleanedContent.replace(thinkRemovalRegex, '');

                                            // 如果有思考块，将最后一个标记为最终结果
                                            if (thinkingBlocks.length > 0) {
                                                thinkingBlocks[thinkingBlocks.length - 1].isLast = true;
                                            }

                                            console.log('从内容中提取到思考块数量:', thinkingBlocks.length);

                                            // 获取现有的思考步骤
                                            const existingThinkingSteps = messages.value[thinkingMessageIndex].thinkingSteps || [];
                                            console.log('现有思考步骤数量:', existingThinkingSteps.length);

                                            // 更新消息标记为打字完成，并显示参考文献
                                            console.log('设置最终消息对象(r1模式)，思考时间:', thinkingTime);

                                            // 只有<think>标签内的内容才是思考过程
                                            let finalThinkingSteps = [];

                                            // 只有从<think>标签中提取的内容才作为思考步骤
                                            if (thinkingBlocks.length > 0) {
                                                finalThinkingSteps = thinkingBlocks;
                                                console.log('使用<think>标签内容作为思考步骤');
                                            } else {
                                                // 没有<think>标签，不显示思考步骤
                                                finalThinkingSteps = [];
                                                console.log('没有<think>标签，不显示思考步骤');
                                            }

                                            // 确保思考步骤不为空
                                            if (finalThinkingSteps.length > 0) {
                                                console.log('最终设置思考步骤，数量:', finalThinkingSteps.length);
                                            }

                                            // 获取当前消息的所有属性
                                            const currentMessage = { ...messages.value[thinkingMessageIndex] };

                                            // 对清理后的内容进行markdown处理
                                            const processedCleanedContent = processContent(cleanedContent);
                                            console.log('流式响应: 对清理后的内容进行markdown处理完成');

                                            // 创建更新后的消息，保留所有现有属性
                                            const updatedMessage = {
                                                ...currentMessage,
                                                // 使用经过markdown处理的内容用于显示
                                                content: processedCleanedContent,
                                                thinkingTime,
                                                showThinkingComplete: false, // 不显示"已完成深度思考"提示
                                                typingCompleted: true, // 标记打字效果已完成
                                                responseCompleted: true, // 表示响应已完全处理完成
                                                showReferences: true,
                                                // 设置思考步骤
                                                thinkingSteps: finalThinkingSteps,
                                                // 始终展开思考步骤，确保它们可见
                                                showThinkingSteps: true,
                                                // 保留思考块内容，不允许被移除
                                                preserveThinking: true
                                            };

                                            // 更新消息
                                            messages.value[thinkingMessageIndex] = updatedMessage;

                                            // 再次滚动到底部确保参考文献可见
                                            // nextTick(() => scrollToBottom());

                                            // 设置全局标记表示此条消息已完全处理完毕
                                            window.lastMessageFullyProcessed = true;

                                            // 触发自定义事件，通知完成
                                            document.dispatchEvent(new CustomEvent('thinking-completed', {
                                                detail: { messageIndex: thinkingMessageIndex }
                                            }));

                                            // 确保思考步骤始终可见
                                            ensureThinkingStepsVisible();
                                        }, 1000); // 缩短延迟时间
                                    }
                                }
                                // 如果没有收到回答，显示接口返回的内容（如果有）
                                else if (!latestAnswer && validAnswers.length > 0) {
                                    // 获取最新的接口返回内容
                                    const currentAnswer = validAnswers[validAnswers.length - 1];

                                    // 处理当前答案，移除<think>标签
                                    let processedCurrentAnswer = processContent(currentAnswer);
                                    const hasThinkTag = processedCurrentAnswer.includes('<think>');

                                    if (hasThinkTag) {
                                        // 从内容中移除<think>标签及其内容
                                        const thinkRegexForRemoval = /<think>([\s\S]*?)<\/think>/g;
                                        processedCurrentAnswer = processedCurrentAnswer.replace(thinkRegexForRemoval, '');
                                    }

                                    // 更新消息显示当前答案
                                    messages.value[thinkingMessageIndex] = {
                                        type: 'system',
                                        content: processedCurrentAnswer || currentAnswer, // 如果处理后的内容为空，则使用原始内容
                                        originalContent: currentAnswer,
                                        thinking: false,
                                        references: null,
                                        typingCompleted: false,
                                        responseCompleted: false,
                                        showReferences: false
                                    };
                                }
                            }
                        }
                    }
                } catch (e) {
                    console.error('处理响应数据失败:', e);
                }
            },
            signal: signal // 使用对象属性语法，避免重复声明
        };

        try {
            console.log('发送API请求，配置:', {
                url: apiUrl,
                mode: selectedMode.value.value,
                kbId: requestData.kbId !== undefined ? requestData.kbId : '未设置',
                repoId: requestData.repoId !== undefined ? requestData.repoId : '未设置',
                session_id: sessionId.value
            });

            const response = await axios(config)
            console.log('API请求完成，状态码:', response.status);

            if (response.status !== 200) {
                throw new Error(`发送消息失败: ${response.status}`);
            }

            // 确保最终内容已完全显示
            if (accumulatedText) {
                console.log('使用积累的文本设置最终消息:', accumulatedText);

                // 获取现有消息属性，保留所有现有属性
                const currentMessage = { ...messages.value[thinkingMessageIndex] };
                const existingThinkingSteps = currentMessage.thinkingSteps || [];

                // 计算思考用时
                const thinkingEndTime = Date.now();
                const thinkingStartTime = currentMessage.thinkingStartTime || thinkingEndTime;
                const thinkingTime = Math.max(1, Math.round((thinkingEndTime - thinkingStartTime) / 1000)); // 转换为秒，最小为1秒
                console.log('思考完成，开始时间:', thinkingStartTime, '结束时间:', thinkingEndTime, '用时:', thinkingTime, '秒');

                // 提取思考块内容，确保保存到思考步骤中
                const thinkingBlocks = [];
                let cleanedContent = accumulatedText.slice();

                // 提取所有thinking-block内容
                const thinkingBlockRegex = /<div class="thinking-block[^"]*"[^>]*>([\s\S]*?)<\/div>/g;
                let thinkMatch;
                let stepCount = 1;

                while ((thinkMatch = thinkingBlockRegex.exec(accumulatedText)) !== null) {
                    // 获取思考内容
                    let thinkContent = thinkMatch[1];

                    // 如果内容超过1000字符，截断并添加提示
                    if (thinkContent.length > 1000) {
                        thinkContent = thinkContent.substring(0, 1000) +
                            '<div style="font-style:italic; color:#666; margin-top:8px;">... (内容已截断，显示前1000字符)</div>';
                    }

                    thinkingBlocks.push({
                        step: stepCount++,
                        content: thinkContent,
                        isLast: false
                    });

                    // 从主内容中移除思考块，避免重复显示
                    cleanedContent = cleanedContent.replace(thinkMatch[0], '');
                }

                // 如果有思考块，将最后一个标记为最终结果
                if (thinkingBlocks.length > 0) {
                    thinkingBlocks[thinkingBlocks.length - 1].isLast = true;
                    console.log('从累积内容中提取思考块，并清理主内容');
                }

                console.log('从积累内容中提取到思考块数量:', thinkingBlocks.length);
                console.log('现有思考步骤数量:', existingThinkingSteps.length);

                // 确定最终使用的思考步骤
                let finalThinkingSteps;

                // 优先使用新提取的思考块，然后是现有思考步骤
                if (thinkingBlocks.length > 0) {
                    finalThinkingSteps = thinkingBlocks;
                    console.log('使用新提取的思考块');
                } else if (existingThinkingSteps.length > 0) {
                    finalThinkingSteps = existingThinkingSteps;
                    console.log('保留现有思考步骤');
                } else {
                    // 如果没有<think>标签，则不显示思考过程
                    finalThinkingSteps = [];
                    console.log('没有<think>标签，不显示思考过程');
                }

                // 设置完整内容
                console.log('设置最终消息对象(备用路径)，思考时间:', thinkingTime);

                // 从原始内容中提取<think>标签内容并从主内容中移除
                const thinkRegexFinal = /<think>([\s\S]*?)<\/think>/g;
                let thinkMatchFinal;
                const extractedThinkingBlocksFinal = [];
                let mainContent = accumulatedText;
                let stepCountFinal = 1;

                // 检查是否包含<think>标签
                const hasThinkTag = accumulatedText.includes('<think>');

                if (hasThinkTag) {
                    console.log('最终内容中检测到<think>标签，提取并移除');
                    // 先提取所有思考内容
                    while ((thinkMatchFinal = thinkRegexFinal.exec(accumulatedText)) !== null) {
                        const fullMatch = thinkMatchFinal[0]; // 完整的<think>...</think>
                        const thinkContent = thinkMatchFinal[1]; // 只有内容部分

                        extractedThinkingBlocksFinal.push({
                            step: stepCountFinal++,
                            content: thinkContent,
                            isLast: false
                        });

                        // 从主内容中移除<think>标签及其内容
                        mainContent = mainContent.replace(fullMatch, '');
                    }

                    if (extractedThinkingBlocksFinal.length > 0) {
                        extractedThinkingBlocksFinal[extractedThinkingBlocksFinal.length - 1].isLast = true;
                        console.log('从最终内容中提取了思考内容，数量:', extractedThinkingBlocksFinal.length);
                    }
                }

                // 合并思考步骤
                let mergedThinkingSteps;
                if (extractedThinkingBlocksFinal.length > 0) {
                    mergedThinkingSteps = extractedThinkingBlocksFinal;
                    console.log('使用从<think>标签提取的思考步骤');
                } else if (thinkingBlocks.length > 0) {
                    mergedThinkingSteps = thinkingBlocks;
                    console.log('使用从thinking-block提取的思考步骤');
                } else if (existingThinkingSteps.length > 0) {
                    mergedThinkingSteps = existingThinkingSteps;
                    console.log('保留现有思考步骤');
                } else {
                    mergedThinkingSteps = [];
                    console.log('没有思考步骤可用');
                }

                // 创建更新后的消息对象，保留所有现有属性
                // 注意：这里的mainContent是从accumulatedText来的，需要重新处理
                let rawContentForDisplay;
                if (hasThinkTag) {
                    // 如果有think标签，需要对mainContent进行处理
                    rawContentForDisplay = mainContent;
                } else {
                    // 否则使用cleanedContent
                    rawContentForDisplay = cleanedContent;
                }

                // 对显示内容进行markdown处理
                const processedContentForDisplay = processContent(rawContentForDisplay);
                console.log('sendMessageToAPI: 对显示内容进行markdown处理完成');

                const updatedMessage = {
                    ...currentMessage,
                    // 使用经过markdown处理的内容用于显示
                    content: processedContentForDisplay,
                    originalContent: latestAnswer, // 保存API返回的原始内容，用于历史记录
                    thinking: false,
                    thinkingTime: thinkingTime,
                    references: latestReferenceChunks.value && latestReferenceChunks.value.length > 0 ? latestReferenceChunks.value : null,
                    docAggs: latestDocAggs.value && latestDocAggs.value.length > 0 ? latestDocAggs.value : null,
                    typingCompleted: true,
                    responseCompleted: true, // 标记响应已完成
                    showReferences: true,
                    thinkingSteps: mergedThinkingSteps, // 使用最终确定的思考步骤
                    showThinkingSteps: true, // 默认展开思考步骤
                    preserveThinking: true // 保留思考块内容，不允许被移除
                };

                // 更新消息
                messages.value[thinkingMessageIndex] = updatedMessage;

                // 确保思考步骤可见
                setTimeout(() => {
                    ensureThinkingStepsVisible();
                }, 100);
            } else if (response.data && !accumulatedText) {
                // 如果没有通过流获取到答案，尝试提取答案
                // 检查多种可能的响应结构
                let answer = null;

                // 打印原始响应数据，不做任何处理
                console.log('未获取到流式回答，原始响应数据:', response.data);
                console.log('响应数据类型:', typeof response.data);
                console.log('响应对象结构:', Object.keys(response.data));

                // 暂时不进行任何处理，只显示正在加载
                const errorMessage2 = "已接收到响应数据，但未能提取有效回答。单位知识库模式下可能需要刷新页面或选择其他知识库后再试。";
                messages.value[thinkingMessageIndex] = {
                    type: 'system',
                    content: processContent(errorMessage2),
                    thinking: false,
                    typingCompleted: true,
                    showReferences: true
                };
            } else if (!accumulatedText) {
                // 如果没有积累到任何文本，可能是单位知识库响应问题
                console.log('未积累到任何回答文本，可能是单位知识库响应问题');
                const errorMessage = "未能获取到有效回答。单位知识库模式下可能需要刷新页面或选择其他知识库后再试。";
                messages.value[thinkingMessageIndex] = {
                    type: 'system',
                    content: processContent(errorMessage),
                    thinking: false,
                    typingCompleted: true,
                    showReferences: true
                };
            }

            await scrollToBottom();
            return true;
        } catch (error) {
            throw error;
        } finally {
            activeController.value = null; // 请求完成后清除控制器引用
            // 确保在API请求完成时重置处理状态
            isProcessing.value = false;
        }
    } catch (error) {
        console.error('发送消息失败:', error);
        // 移除思考状态，显示错误消息
        const lastIndex = messages.value.length - 1;
        if (messages.value[lastIndex] && messages.value[lastIndex].thinking) {
            // 计算思考时间
            const thinkingEndTime = Date.now();
            const thinkingStartTime = messages.value[lastIndex].thinkingStartTime || thinkingEndTime;
            const thinkingTime = Math.max(1, Math.round((thinkingEndTime - thinkingStartTime) / 1000));

            const errorMessage3 = '抱歉，发送消息时出现错误，请稍后重试。';
            messages.value[lastIndex] = {
                type: 'system',
                content: processContent(errorMessage3),
                thinking: false,
                thinkingTime: thinkingTime,
                typingCompleted: true,
                showReferences: true
            };
        }

        // 确保在错误情况下也重置处理状态
        isProcessing.value = false;
        return false;
    }
};

// 适配小屏幕设备的建议问题
const adaptSuggestedQuestions = () => {
    if (isMobile.value && messages.value.length > 0 && messages.value[0].welcome) {
        // 如果是移动设备，减少建议问题数量
        const mobileQuestions = [
            '累计发现问题数',
            '今日发现问题数',
            '已整改问题数',
            '正在解决问题数',
            '全部问题整改率',
            '现状问题状态分布',
            '旗区工作量统计',
            '东康问题总数',
            '近三个月东康问题总数'
        ];
        messages.value[0].suggestedQuestions = mobileQuestions;
    }
};

// 检测设备类型和方向
const checkDeviceAndOrientation = () => {
    // 检测是否为移动设备（宽度小于768px）
    isMobile.value = window.innerWidth < 768;

    // 检测是否为横屏
    isLandscape.value = window.innerWidth > window.innerHeight;

    // 滚动到底部（避免屏幕旋转后消息不可见）
    nextTick(() => {
        scrollToBottom();
    });
};

// 生成时间戳
const generateTimestamp = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 清除当前对话
const clearConversation = () => {
    // 如果有对话内容，则显示确认对话框
    if (messages.value.length > 1) {
        showClearConfirmDialog.value = true;
    } else {
        // 如果只有欢迎消息，直接重置
        resetConversation();
    }
};

// 确认清除当前对话
const confirmClearConversation = async () => {
    // 保存当前对话到历史记录
    if (messages.value.length > 1) {
        saveCurrentConversation();
    }

    // 重新创建会话
    await createSession();

    // 关闭确认对话框
    showClearConfirmDialog.value = false;

    // 显示Toast消息
    showToast('对话已清除');
};

// 保存当前对话到历史记录
const saveCurrentConversation = () => {
    // 只有在不是查看历史记录时才保存
    if (selectedHistoryIndex.value !== -1) {
        return;
    }

    try {
        // 查找第一个用户消息作为标题
        const firstUserMessage = messages.value.find(msg => msg.type == 'user');
        const title = firstUserMessage ? firstUserMessage.content : '未命名对话';

        // 创建唯一ID
        const id = `chat_${Date.now().toString()}`;

        // 处理消息，移除不需要持久化的大数据，但保留<think>标签及其内容
        const processedMessages = JSON.parse(JSON.stringify(messages.value)).map(msg => {
            // 保留基本属性，移除大量参考文献等数据
            const { type, content, originalContent, welcome, suggestedQuestions } = msg;

            // 优先使用originalContent（包含原始的<think>标签内容），如果没有则使用content
            const finalContent = originalContent || content;
            const simplifiedMsg = { type, content: finalContent };

            // 只为欢迎消息保留建议问题
            if (welcome) {
                simplifiedMsg.welcome = true;
                simplifiedMsg.suggestedQuestions = suggestedQuestions || welcomeMessage.suggestedQuestions;
            }

            return simplifiedMsg;
        });

        const conversation = {
            id,
            title,
            messages: processedMessages,
            timestamp: generateTimestamp(),
            sessionId: sessionId.value,
            mode: selectedMode.value
        };

        // 添加到开头
        chatHistory.value.unshift(conversation);

        // 最多保留10条历史记录
        if (chatHistory.value.length > 10) {
            chatHistory.value = chatHistory.value.slice(0, 10);
        }

        // 历史记录已经通过API保存，这里不再需要保存到localStorage
        console.log('对话已保存到历史记录（通过API）');
    } catch (e) {
        console.error('处理历史记录时出错:', e);
    }
};

// 重置对话
const resetConversation = async () => {
    // 清空当前对话内容
    messages.value = [];
    console.log('已清空当前对话内容');

    // 重置参考文献和参考文档数据
    latestReferenceChunks.value = [];
    latestDocAggs.value = [];
    console.log('已重置参考文献和参考文档数据');

    // 创建新会话
    await createSession();
};

// 加载历史对话
const loadHistoryConversation = async (history) => {
    try {
        console.log('加载历史对话:', history);
        console.log('历史对话详细数据:', JSON.stringify(history));

        // 设置选中的历史索引，表明我们正在查看历史记录
        selectedHistoryIndex.value = getHistoryFullIndex(history);
        console.log('已设置历史索引:', selectedHistoryIndex.value);

        // 安全地检查字符串是否以特定前缀开头
        const safeStartsWith = (str, prefix) => {
            return typeof str === 'string' && str.startsWith(prefix);
        };

        // 保存旧的会话ID，以便在提取失败时恢复
        const oldSessionId = sessionId.value;

        // 从多个可能的位置获取会话ID
        let newSessionId = '';

        console.log('历史记录对象的sessionId:', history.sessionId);
        console.log('历史记录对象的apiData:', history.apiData);
        console.log('历史记录对象的apiData.id:', history.apiData?.id);

        // 优先使用history.apiData.conversationId，这是与会话直接关联的ID
        if (history.apiData && history.apiData.conversationId && typeof history.apiData.conversationId == 'string') {
            console.log('使用history.apiData.conversationId作为会话ID:', history.apiData.conversationId);

            // 处理conversationId格式，如果以conv_开头，需要提取出后面的部分
            if (history.apiData.conversationId.startsWith('conv_')) {
                newSessionId = history.apiData.conversationId.substring(5); // 去掉'conv_'前缀
                console.log('从conversationId中提取会话ID:', newSessionId);
            } else {
                newSessionId = history.apiData.conversationId;
            }
        }
        // 如果没有conversationId，尝试使用apiData.id
        else if (history.apiData && history.apiData.id) {
            console.log('使用history.apiData.id作为会话ID:', history.apiData.id);
            newSessionId = `${history.apiData.id}`;
        }
        // 如果没有apiData.id，尝试使用sessionId
        else if (history.sessionId && typeof history.sessionId == 'string') {
            console.log('使用history.sessionId作为会话ID:', history.sessionId);

            // 同样处理sessionId格式
            if (history.sessionId.startsWith('conv_')) {
                newSessionId = history.sessionId.substring(5); // 去掉'conv_'前缀
                console.log('从sessionId中提取会话ID:', newSessionId);
            } else {
                newSessionId = history.sessionId;
            }
        }

        // 更新会话ID并同时保存到localStorage
        if (newSessionId) {
            console.log('从历史记录中提取到的会话ID:', newSessionId);
            console.log('历史记录对象的原始ID:', history.apiData?.id || history.sessionId || '未知');

            // 立即更新sessionId.value和localStorage
            sessionId.value = newSessionId;
            // 保存历史记录ID到localStorage，确保后续能正确调用c
            if (history.apiData && history.apiData.id) {
                localStorage.setItem('current_conversation_id', history.apiData.id);
                console.log('保存历史记录ID到localStorage:', history.apiData.id);
            } else {
                localStorage.setItem('current_conversation_id', history.apiData?.id || '');
            }

            // 强制同步到全局变量，确保其他地方能立即访问到最新值
            window.currentSessionId = newSessionId;

            console.log('设置的会话ID:', sessionId.value);
            console.log('设置的历史记录ID (localStorage):', localStorage.getItem('current_conversation_id'));

            // 验证设置是否成功
            console.log('验证设置后的sessionId.value:', sessionId.value);
            console.log('验证设置后的localStorage:', localStorage.getItem('current_conversation_id'));
        } else {
            console.warn('未能从历史记录中提取有效的会话ID');
            // 如果历史记录中有apiData.id，使用它作为历史记录ID
            if (history.apiData && history.apiData.id) {
                console.log('使用历史记录中的apiData.id:', history.apiData.id);
                localStorage.setItem('current_conversation_id', history.apiData.id);
                // 尝试使用历史记录ID作为会话ID
                sessionId.value = history.apiData.id;
            } else {
                console.error('历史记录中没有有效的ID信息，无法恢复会话');
                showToast('无法恢复历史会话，请尝试其他操作', 'error');
                // 不设置会话ID，等待用户操作
                sessionId.value = '';
                localStorage.removeItem('current_conversation_id');
            }
        }

        // 准备新的消息列表
        let newMessages = [];
        let chatHistoryData = null;

        // 如果有API原始数据，尝试从不同位置提取消息历史
        if (history.apiData) {
            console.log('处理apiData中的历史记录');
            try {
                // 1. 尝试从apiData.chatHistory获取
                if (history.apiData.chatHistory) {
                    console.log('尝试从apiData.chatHistory获取数据:',
                        typeof history.apiData.chatHistory,
                        history.apiData.chatHistory.substring ? history.apiData.chatHistory.substring(0, 100) + '...' : '');

                    // 解析chatHistory字段，可能是字符串或已经是对象
                    if (typeof history.apiData.chatHistory === 'string') {
                        // 处理转义字符问题 - 字符串可能被双重转义
                        let chatHistoryStr = history.apiData.chatHistory;

                        // 显示原始字符串的前100个字符用于调试
                        console.log('原始chatHistory字符串前100个字符:',
                            chatHistoryStr.substring(0, 100) + '...');

                        // 尝试直接解析
                        try {
                            chatHistoryData = JSON.parse(chatHistoryStr);
                            console.log('成功直接解析chatHistory字符串');
                        } catch (parseError) {
                            console.error('直接解析chatHistory字符串失败，尝试处理转义字符:', parseError);

                            // 处理各种转义情况
                            try {
                                // 处理双重转义
                                if (chatHistoryStr.includes('\\"')) {
                                    console.log('检测到转义引号，进行处理');
                                    chatHistoryStr = chatHistoryStr.replace(/\\"/g, '"');
                                }

                                // 处理转义反斜杠
                                if (chatHistoryStr.includes('\\\\')) {
                                    console.log('检测到转义反斜杠，进行处理');
                                    chatHistoryStr = chatHistoryStr.replace(/\\\\/g, '\\');
                                }

                                // 如果是以引号开始和结束的字符串，可能是被额外引号包围的JSON
                                if (chatHistoryStr.startsWith('"') && chatHistoryStr.endsWith('"')) {
                                    console.log('检测到外层引号，尝试移除');
                                    // 移除外层引号并处理内部转义
                                    chatHistoryStr = chatHistoryStr.substring(1, chatHistoryStr.length - 1);
                                    // 可能需要额外处理JSON内部的转义
                                    chatHistoryStr = chatHistoryStr.replace(/\\"/g, '"');
                                }

                                // 再次尝试解析
                                chatHistoryData = JSON.parse(chatHistoryStr);
                                console.log('处理转义后成功解析');
                            } catch (e) {
                                console.error('处理转义后解析仍然失败:', e);

                                // 最后尝试 - 如果字符串看起来像是数组形式，但被引号包围
                                if (chatHistoryStr.includes('[{') && chatHistoryStr.includes('}]')) {
                                    console.log('尝试提取数组部分');
                                    const startIdx = chatHistoryStr.indexOf('[');
                                    const endIdx = chatHistoryStr.lastIndexOf(']') + 1;
                                    if (startIdx >= 0 && endIdx > startIdx) {
                                        const arrayPart = chatHistoryStr.substring(startIdx, endIdx);
                                        try {
                                            chatHistoryData = JSON.parse(arrayPart);
                                            console.log('成功提取并解析数组部分');
                                        } catch (arrayError) {
                                            console.error('解析提取的数组部分失败:', arrayError);
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        // 已经是对象
                        chatHistoryData = history.apiData.chatHistory;
                        console.log('使用对象形式的chatHistory');
                    }
                }

                // 确保chatHistoryData是数组
                if (!Array.isArray(chatHistoryData)) {
                    console.error('chatHistoryData不是数组或为空:', chatHistoryData);
                    chatHistoryData = [];
                }

                // 如果数组为空，尝试其他方法获取消息
                if (chatHistoryData.length === 0) {
                    console.log('chatHistoryData为空，尝试从history.messages获取');
                    if (history.messages && history.messages.length > 0) {
                        chatHistoryData = history.messages;
                    }
                }

                // 转换消息格式，确保保留完整对话内容
                if (chatHistoryData && chatHistoryData.length > 0) {
                    console.log('使用chatHistoryData创建消息列表, 条目数:', chatHistoryData.length);

                    try {
                        // 先处理可能的欢迎消息
                        const hasWelcomeMessage = chatHistoryData.some(msg =>
                            (msg.role == 'assistant' || msg.role == 'system') &&
                            msg.content &&
                            msg.content.includes('您好')
                        );

                        if (!hasWelcomeMessage) {
                            // 如果没有欢迎消息，添加一个
                            newMessages.push({
                                type: 'system',
                                content: '您好，我是智能助手，有什么可以帮您？',
                                welcome: true,
                                typingCompleted: true,
                                showReferences: true,
                                suggestedQuestions: welcomeMessage.suggestedQuestions
                            });
                        }

                        // 遍历聊天历史添加消息
                        // 暂时禁用去重功能，直接处理所有消息
                        const processedMessages = [];

                        chatHistoryData.forEach(msg => {
                            try {
                                // 确保能识别不同形式的role标识
                                const isUserMessage =
                                    msg.role === 'user' ||
                                    msg.role === 'User' ||
                                    msg.type === 'user';

                                // 获取消息内容
                                let messageContent = msg.content || '';

                                // 仅处理THINKPLACEHOLDER标记，保留其他所有HTML格式
                                if (messageContent && !isUserMessage && messageContent.includes('THINKPLACEHOLDER')) {
                                    // 移除THINKPLACEHOLDER标记，这些是思考过程的占位符
                                    messageContent = messageContent.replace(/THINKPLACEHOLDER\d+/g, '');
                                    console.log('处理了assistant消息中的THINKPLACEHOLDER标记');
                                }

                                // 确保内容不为空
                                if (messageContent.trim() !== '') {
                                    // 尝试解析复杂的JSON格式内容（可能包含思考步骤和参考文档）
                                    let parsedContent = messageContent;
                                    let thinkingSteps = [];
                                    let docAggs = [];

                                    if (!isUserMessage) {
                                        try {
                                            // 尝试解析为JSON对象
                                            const contentObj = JSON.parse(messageContent);
                                            if (contentObj && typeof contentObj === 'object') {
                                                console.log('成功解析助手消息为JSON对象');

                                                // 提取实际内容
                                                if (contentObj.content) {
                                                    parsedContent = contentObj.content;
                                                    console.log('从JSON中提取content字段');
                                                }

                                                // 提取思考步骤
                                                if (Array.isArray(contentObj.thinkingSteps) && contentObj.thinkingSteps.length > 0) {
                                                    thinkingSteps = contentObj.thinkingSteps;
                                                    console.log('从JSON中提取思考步骤:', thinkingSteps.length);

                                                    // 处理思考步骤内容并去重
                                                    // 首先处理每个步骤的内容
                                                    const processedSteps = thinkingSteps.map((step, index) => {
                                                        // 如果step是null或undefined，创建一个新对象
                                                        if (!step) {
                                                            console.warn(`历史记录中第${index + 1}个思考步骤为空，已创建新对象`);
                                                            return {
                                                                step: index + 1,
                                                                content: '思考步骤内容',
                                                                isLast: false // 先统一设为false，后面再更新
                                                            };
                                                        }

                                                        let stepContent = '';
                                                        // 如果step.content为空，添加默认内容
                                                        if (!step.content) {
                                                            console.warn(`历史记录中第${index + 1}个思考步骤内容为空，已添加默认内容`);
                                                            stepContent = '思考步骤内容';
                                                        } else {
                                                            // 处理思考步骤内容，确保是纯文本
                                                            if (typeof step.content === 'string') {
                                                                // 检查是否包含HTML代码
                                                                if (step.content.includes('style=') ||
                                                                    step.content.includes('data-preserve="true"') ||
                                                                    (step.content.includes('<') && step.content.includes('>'))) {
                                                                    console.log(`历史记录中第${index + 1}个思考步骤内容包含HTML代码，进行处理`);
                                                                    stepContent = processThinkingContent(step.content);
                                                                } else {
                                                                    stepContent = step.content;
                                                                }
                                                            } else {
                                                                stepContent = String(step.content);
                                                            }
                                                        }

                                                        // 返回处理后的步骤
                                                        return {
                                                            ...step,
                                                            step: step.step || (index + 1),
                                                            content: stepContent,
                                                            isLast: false // 先统一设为false，后面再更新
                                                        };
                                                    });

                                                    // 然后去重
                                                    const uniqueSteps = [];
                                                    const seenContents = new Set();

                                                    processedSteps.forEach(step => {
                                                        if (step && step.content && !seenContents.has(step.content)) {
                                                            seenContents.add(step.content);
                                                            uniqueSteps.push(step);
                                                        }
                                                    });

                                                    // 更新最后一个步骤的isLast标记
                                                    if (uniqueSteps.length > 0) {
                                                        uniqueSteps[uniqueSteps.length - 1].isLast = true;
                                                    }

                                                    // 使用去重后的步骤
                                                    thinkingSteps = uniqueSteps;
                                                }

                                                // 处理保留思考标志
                                                if (contentObj.preserveThinking !== undefined) {
                                                    preserveThinking = contentObj.preserveThinking;
                                                } else {
                                                    // 如果有思考步骤但没有preserveThinking标志，默认设为true
                                                    preserveThinking = thinkingSteps.length > 0 ? true : preserveThinking;
                                                }

                                                // 提取参考文档
                                                if (Array.isArray(contentObj.docAggs) && contentObj.docAggs.length > 0) {
                                                    docAggs = contentObj.docAggs;
                                                    console.log('从JSON中提取参考文档:', docAggs.length);
                                                }
                                            }
                                        } catch (parseError) {
                                            // 如果解析失败，使用原始内容
                                            console.log('解析消息内容为JSON失败，使用原始内容');
                                            parsedContent = messageContent;
                                        }
                                    }

                                    // 对系统消息使用processContent处理，确保样式正确应用
                                    // 用户消息保持原样
                                    let processedContent = '';

                                    if (isUserMessage) {
                                        processedContent = messageContent;
                                    } else {
                                        // 确保解析的内容是纯文本，而不是JSON对象
                                        if (typeof parsedContent === 'object') {
                                            console.warn('解析后的内容仍然是对象，转换为字符串');
                                            parsedContent = JSON.stringify(parsedContent);
                                        }

                                        // 如果内容是JSON字符串，尝试提取实际内容
                                        if (typeof parsedContent === 'string' && parsedContent.trim().startsWith('{')) {
                                            try {
                                                const contentObj = JSON.parse(parsedContent);
                                                if (contentObj && contentObj.content) {
                                                    parsedContent = contentObj.content;
                                                    console.log('从嵌套JSON中提取content字段');
                                                }
                                            } catch (e) {
                                                console.warn('尝试解析嵌套JSON失败，使用原始内容');
                                            }
                                        }

                                        // 处理历史记录内容，保留思考过程的样式
                                        if (typeof parsedContent === 'string') {
                                            // 检查并解析[FILELIST][/FILELIST]格式的参考文档
                                            const fileListMatch = parsedContent.match(/\[FILELIST\](.*?)\[\/FILELIST\]/);
                                            if (fileListMatch) {
                                                const fileListContent = fileListMatch[1];
                                                if (fileListContent.trim()) {
                                                    try {
                                                        // 尝试解析JSON格式的参考文档数据
                                                        docAggs = JSON.parse(fileListContent);
                                                        console.log('从历史记录中解析出参考文档:', docAggs);
                                                    } catch (e) {
                                                        // 如果JSON解析失败，尝试按旧格式解析（逗号分隔的文件名）
                                                        console.warn('JSON解析失败，尝试按旧格式解析:', e);
                                                        const fileNames = fileListContent.split(',').map(name => name.trim()).filter(name => name);
                                                        docAggs = fileNames.map((fileName, index) => ({
                                                            doc_id: `file_${index}`,
                                                            doc_name: fileName
                                                        }));
                                                        console.log('按旧格式解析出参考文档:', docAggs);
                                                    }
                                                }
                                                // 从内容中移除[FILELIST][/FILELIST]标签
                                                parsedContent = parsedContent.replace(/\[FILELIST\].*?\[\/FILELIST\]/, '');
                                            }

                                            // 检查是否需要反转义HTML内容（针对内容创作联网模式保存的数据）
                                            // 通过检查是否包含HTML实体来判断是否需要反转义
                                            if (parsedContent.includes('&lt;') || parsedContent.includes('&gt;') ||
                                                parsedContent.includes('&quot;') || parsedContent.includes('&#39;') ||
                                                parsedContent.includes('&amp;')) {
                                                console.log('检测到HTML转义内容，进行反转义处理');
                                                parsedContent = unescapeHtmlContent(parsedContent);
                                                console.log('反转义处理完成，内容长度:', parsedContent.length);
                                                console.log('反转义后内容预览:', parsedContent.substring(0, 200));
                                            }

                                            // 不再移除<think>标签，而是通过processContent函数转换为带样式的显示
                                            // 这样历史记录中的思考过程也能正确显示样式

                                            // 移除所有thinking-block类的div及其内容，避免在正文中重复显示
                                            parsedContent = parsedContent.replace(/<div[^>]*class="thinking-block[^"]*"[^>]*>[\s\S]*?<\/div>/g, '');

                                            // 移除所有带有data-preserve="true"的div及其内容
                                            parsedContent = parsedContent.replace(/<div[^>]*data-preserve="true"[^>]*>[\s\S]*?<\/div>/g, '');

                                            // 不移除思考块样式，让<think>和<thinking>标签能正确显示
                                        }

                                        // 使用processContent处理内容，包括转换<think>和<thinking>标签为带样式的显示
                                        console.log('历史记录处理前的内容:', parsedContent);
                                        console.log('是否包含<thinking>标签:', parsedContent.includes('<thinking>'));
                                        processedContent = processContent(parsedContent);
                                        console.log('历史记录处理后的内容:', processedContent);
                                    }

                                    // 创建标准化的消息对象
                                    const messageObj = {
                                        type: isUserMessage ? 'user' : 'system',
                                        content: processedContent, // 使用处理后的内容，确保HTML格式和样式正确应用
                                        originalContent: parsedContent, // 保存解析后的实际内容
                                        typingCompleted: true,
                                        showReferences: docAggs.length > 0, // 如果有参考文档就显示参考区域
                                        timestamp: msg.timestamp || Date.now().toString(),
                                        thinkingSteps: thinkingSteps.length > 0 ? thinkingSteps : null, // 添加思考步骤
                                        docAggs: docAggs.length > 0 ? docAggs : null, // 添加参考文档
                                        showThinkingSteps: true, // 默认展开思考步骤，确保在历史记录中可见
                                        preserveThinking: true // 确保思考步骤被保留
                                    };

                                    // 直接添加消息，不进行去重
                                    processedMessages.push(messageObj);
                                }
                            } catch (msgError) {
                                console.error('处理单条消息时出错:', msgError);
                            }
                        });

                        // 将处理后的消息添加到newMessages数组
                        newMessages = [...newMessages, ...processedMessages];

                        // 去除第一条用户发送的数据
                        const firstUserMessageIndex = newMessages.findIndex(msg => msg.type === 'user');
                        if (firstUserMessageIndex !== -1) {
                            console.log('找到第一条用户消息，准备移除:', newMessages[firstUserMessageIndex]);
                            newMessages.splice(firstUserMessageIndex, 1);
                            console.log('已移除第一条用户消息');
                        }

                        console.log('最终转换的消息数组长度:', newMessages.length);
                    } catch (convError) {
                        console.error('转换整个对话出错:', convError);
                    }

                    console.log('转换后的消息列表:', newMessages);
                }
            } catch (e) {
                console.error('处理历史消息失败，详细错误:', e);
                console.error('错误栈:', e.stack);
                // 失败后清空消息列表，从头开始
                newMessages = [];
            }
        }

        // 尝试直接使用history.messages作为备选
        if (newMessages.length === 0 && history.messages && history.messages.length > 0) {
            console.log('使用history.messages数据');
            newMessages = [...history.messages];

            // 去除第一条用户发送的数据
            const firstUserMessageIndex = newMessages.findIndex(msg => msg.type === 'user');
            if (firstUserMessageIndex !== -1) {
                console.log('在备选数据中找到第一条用户消息，准备移除:', newMessages[firstUserMessageIndex]);
                newMessages.splice(firstUserMessageIndex, 1);
                console.log('已移除备选数据中的第一条用户消息');
            }

            console.log('使用的history.messages:', newMessages);
        }

        // 如果没有消息，添加一个默认的欢迎消息
        if (newMessages.length === 0) {
            console.log('没有找到历史消息，添加默认欢迎消息');
            newMessages = [{
                type: 'system',
                content: '欢迎使用智能助手',
                welcome: true,
                typingCompleted: true,
                showReferences: true,
                suggestedQuestions: welcomeMessage.suggestedQuestions
            }];
        } else {
            // 标记最后一条系统消息为已完成，确保不会出现思考中状态
            for (let i = newMessages.length - 1; i >= 0; i--) {
                if (newMessages[i].type === 'system') {
                    newMessages[i].typingCompleted = true;
                    newMessages[i].showReferences = true;
                    newMessages[i].thinking = false;
                    break;
                }
            }
        }

        // 设置消息列表 - 注意：这里不需要再次设置selectedHistoryIndex，因为我们在函数开始时已经设置过了
        console.log('最终设置的消息列表:', newMessages);
        messages.value = newMessages;

        // 设置当前模式
        // 设置全局标志，表明这是从历史记录加载时设置的模式
        window.isSettingModeFromHistory = true;
        window.isManualModeChange = false; // 不是手动切换
        console.log('从历史记录加载模式，设置标志:', window.isSettingModeFromHistory);

        // 根据历史记录设置模式
        if (history.apiData && history.apiData.conversationType) {
            switch (history.apiData.conversationType) {
                case 'DATA_QUERY':
                    console.log('根据历史记录设置为i暖城问数模式');
                    selectedMode.value = modeOptions.find(mode => mode.value === 'v2') || modeOptions[1];
                    break;
                case 'CONTENT_CREATION':
                    console.log('根据历史记录设置为内容创作模式');
                    selectedMode.value = modeOptions.find(mode => mode.value === 'content_creation') || modeOptions[2];
                    break;
                case 'POLICY_QA':
                    console.log('根据历史记录设置为政策问答模式');
                    selectedMode.value = modeOptions.find(mode => mode.value === 'policy_qa') || modeOptions[3];
                    break;
                case 'WORK_REPORT':
                    console.log('根据历史记录设置为创城工作报告模式');
                    selectedMode.value = modeOptions.find(mode => mode.value === 'WORK_REPORT') || modeOptions[4];
                    break;
                case 'DAILY_QA':
                default:
                    console.log('根据历史记录设置为日常问答模式');
                    selectedMode.value = modeOptions.find(mode => mode.value === 'r1') || modeOptions[0];
                    break;
            }
        } else if (history.mode) {
            console.log('使用历史记录中保存的模式');
            // 确保模式存在于当前的模式选项中
            const foundMode = modeOptions.find(mode => mode.value === history.mode.value);
            selectedMode.value = foundMode || modeOptions[0]; // 如果找不到则使用默认模式
        } else {
            // 向后兼容，没有模式信息的旧历史记录
            console.log('历史记录没有模式信息，使用默认模式');
            selectedMode.value = modeOptions[0]; // 默认使用第一个模式
        }

        // 确保模式设置后检查标志状态
        nextTick(() => {
            console.log('模式设置完成后的标志状态:', window.isSettingModeFromHistory);
            // 如果标志仍然为true，说明watch还没有触发，手动触发一次检查
            if (window.isSettingModeFromHistory === true) {
                console.log('模式设置后标志仍为true，可能watch尚未触发');
            }
        });

        // 打印设置后的模式信息进行调试
        console.log('最终设置的模式: ', selectedMode.value);
        console.log('模式的value属性: ', selectedMode.value.value);

        // 设置知识库选择
        if (history.apiData) {
            console.log('历史记录中的知识库ID:', history.apiData.repoId);

            // 如果repoId为null或undefined，表示单位知识库
            if (history.apiData.repoId === null || history.apiData.repoId === undefined) {
                selectedKnowledgeBase.value = {
                    text: '单位知识库（全部）',
                    value: 'unit'
                };
                console.log('根据历史记录设置为单位知识库');
            } else {
                // 查找匹配的知识库
                const repo = repoList.value.find(r => r.repoId == history.apiData.repoId);
                if (repo) {
                    selectedKnowledgeBase.value = {
                        text: repo.repoDesc,
                        value: 'repo_' + repo.repoId
                    };
                    console.log('根据历史记录设置知识库为:', repo.repoDesc);
                } else {
                    console.log('未找到匹配的知识库，使用ID:', history.apiData.repoId);
                    // 如果没有匹配的知识库，创建一个临时的
                    selectedKnowledgeBase.value = {
                        text: '知识库 ' + history.apiData.repoId,
                        value: 'repo_' + history.apiData.repoId
                    };
                }
            }
        }

        // 显示Toast消息包含ID信息，方便调试
        // showToast(`已加载历史对话 (ID: ${newSessionId})`);

        // 控制台输出当前会话ID以供调试
        console.log('===== 历史记录加载信息 =====');
        console.log('当前会话ID:', sessionId.value);
        console.log('localStorage中的会话ID:', localStorage.getItem('current_conversation_id'));
        console.log('history对象中的apiData.id:', history.apiData?.id || '无apiData.id');
        console.log('history对象中的完整id值:', history.apiData?.id);
        console.log('history对象中的完整apiData:', history.apiData);
        console.log('全局window对象中的会话ID:', window.currentSessionId);
        console.log('==========================');

        // 重要: 不再调用createSession或addHistory，因为我们已经从历史记录中获取了会话ID
        // 点击历史记录查看内容时，不需要重新获取历史记录列表，避免多余的API调用

        // 确保localStorage中有正确的历史记录ID，用于后续addMessage调用
        if (history.apiData && history.apiData.id) {
            localStorage.setItem('current_conversation_id', history.apiData.id);
            console.log('加载历史记录后确认localStorage中的ID:', history.apiData.id);
        }

        // 确保DOM更新后思考步骤可见
        nextTick(() => {
            console.log('历史记录加载完成，确保思考步骤可见');
            ensureThinkingStepsVisible();

            // 只在聊天容器内滚动，不影响整个页面
            if (chatContainer.value) {
                chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
            }
        });

        // 添加单个延迟，确保状态已正确设置
        setTimeout(() => {
            console.log('历史记录加载完成后的状态检查:');
            console.log('- 会话ID:', sessionId.value);
            console.log('- 历史索引:', selectedHistoryIndex.value);
            console.log('- 消息数量:', messages.value.length);

            // 确保思考步骤可见
            ensureThinkingStepsVisible();

            // 再次确保滚动到底部
            if (chatContainer.value) {
                chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
            }
        }, 300);
    } catch (e) {
        console.error('加载历史对话失败:', e);
        showToast('加载对话失败', 'error');
    }
};

// 清空历史记录
const clearHistory = async () => {
    // 显示确认对话框
    showClearHistoryConfirmDialog.value = true;
};

// 确认清空历史记录
const confirmClearHistory = async () => {
    try {
        // 获取所有历史记录的ID - 需要从每个分组中获取
        const allHistoryIds = [
            ...chatHistory.value.recent7Days.filter(history => history.apiData && history.apiData.id).map(history => history.apiData.id),
            ...chatHistory.value.recent30Days.filter(history => history.apiData && history.apiData.id).map(history => history.apiData.id),
            ...chatHistory.value.before30Days.filter(history => history.apiData && history.apiData.id).map(history => history.apiData.id)
        ];

        console.log('准备删除的历史记录ID列表:', allHistoryIds);

        // 记录当前是否在查看历史记录
        const wasViewingHistory = selectedHistoryIndex.value !== -1;
        console.log('正在查看历史记录:', wasViewingHistory);

        if (allHistoryIds.length > 0) {
            // 调用删除API，传递所有ID
            const response = await removeMessage({
                ids: allHistoryIds.join(',')
            });

            console.log('批量删除历史记录API响应:', response);

            if (response.code != 0) {
                console.error('批量删除失败:', response);
                throw new Error('批量删除失败');
            }
        }

        // 清空本地历史记录数组
        chatHistory.value = {
            recent7Days: [],
            recent30Days: [],
            before30Days: []
        };

        // 如果之前正在查看历史记录，则清除当前对话并创建新会话
        if (wasViewingHistory) {
            console.log('历史记录已清空且正在查看历史记录，重新开启新对话');
            selectedHistoryIndex.value = -1;

            // 重新创建会话，这会清除当前对话内容
            await createNewSession(false);
        } else {
            // 仅重置历史索引
            selectedHistoryIndex.value = -1;
        }

        showToast('历史记录已清空');

        // 重新获取历史记录（应该为空）
        await fetchHistoryFromAPI();

        // 关闭确认对话框
        showClearHistoryConfirmDialog.value = false;
    } catch (e) {
        console.error('清空历史记录失败:', e);
        // showToast('清空历史记录失败', 'error');
        // 关闭确认对话框
        showClearHistoryConfirmDialog.value = false;
    }
};

// 获取历史记录标题
const getHistoryTitle = (history) => {
    // 优先使用API数据中的标题
    const title = history.apiData ? history.apiData.conversationTitle : history.title;
    if (!title) return '未命名对话';
    return title.length > 30 ? title.substring(0, 30) + '...' : title;
};

// 从API获取历史记录
const fetchHistoryFromAPI = async (resetSelection = false) => {
    try {
        console.log('从API获取历史记录');
        const response = await getMessage();

        if (response.code == 0 && response.data) {
            console.log('API返回的历史记录数量:',
                (response.data.recent7Days?.length || 0) +
                (response.data.recent30Days?.length || 0) +
                (response.data.before30Days?.length || 0));

            // 直接使用API返回的分组数据
            // 更新历史记录，保持分组结构
            chatHistory.value = {
                recent7Days: (response.data.recent7Days || []).map(enrichHistoryItem),
                recent30Days: (response.data.recent30Days || []).map(enrichHistoryItem),
                before30Days: (response.data.before30Days || []).map(enrichHistoryItem)
            };

            // 计算历史记录总数
            const totalCount =
                chatHistory.value.recent7Days.length +
                chatHistory.value.recent30Days.length +
                chatHistory.value.before30Days.length;

            console.log('历史记录已更新，共', totalCount, '条，按分组格式存储');

            // 只有在明确要求重置选中状态时才重置
            if (resetSelection) {
                selectedHistoryIndex.value = -1;
                console.log('已重置历史记录选中状态');
            }

            // 初始化分组展开状态
            if (!groupExpanded.value.hasOwnProperty('oldest')) {
                groupExpanded.value.oldest = true;
            }
        } else {
            console.error('暂无历史记录:', response?.msg || '未知错误');
            // showToast('获取历史记录失败', 'error');
        }
    } catch (error) {
        console.error('获取历史记录异常:', error);
        // showToast('获取历史记录失败', 'error');
    }
};

// 立即设置选中状态（在调用addMessage时）
const setSelectedHistoryImmediately = (historyId, isHistoryMode = false) => {
    console.log('立即设置选中状态，历史记录ID:', historyId, '是否历史记录模式:', isHistoryMode);

    if (isHistoryMode) {
        // 历史记录模式下，数据会跑到第一个位置，立即设置选中状态为第一个
        selectedHistoryIndex.value = 0;
        console.log('历史记录模式：已立即更新选中状态为第一个位置（索引0）');
    } else {
        // 新创建对话模式下，重置为-1表示不再查看历史记录，而是在当前新创建的对话中
        selectedHistoryIndex.value = -1;
        console.log('新对话模式：已立即更新选中状态，表示当前在新创建的对话中');
    }
};

// 选中新创建的历史记录
const selectNewlyCreatedHistory = (historyId) => {
    console.log('尝试选中新创建的历史记录，ID:', historyId);

    // 在所有分组中查找匹配的历史记录
    const allHistories = [
        ...chatHistory.value.recent7Days,
        ...chatHistory.value.recent30Days,
        ...chatHistory.value.before30Days
    ];

    // 查找匹配的历史记录，通过enrichHistoryItem设置的id格式查找
    const targetHistory = allHistories.find(history => {
        // enrichHistoryItem设置的id格式是 `api_${item.id}`
        const expectedId = `api_${historyId}`;
        return history.id === expectedId;
    });

    if (targetHistory) {
        const fullIndex = getHistoryFullIndex(targetHistory);
        selectedHistoryIndex.value = fullIndex;
        console.log('已选中新创建的历史记录，索引:', fullIndex, '历史记录ID:', targetHistory.id);
    } else {
        console.warn('未找到新创建的历史记录，期望ID: api_' + historyId);
        console.log('当前所有历史记录ID:', allHistories.map(h => h.id));
    }
};

// 处理历史记录项，添加必要的属性
const enrichHistoryItem = (item) => {
    console.log('处理历史记录项，ID:', item.id, '会话ID:', item.conversationId);

    // 尝试解析聊天历史
    let chatHistoryData = [];
    try {
        if (item.chatHistory) {
            if (typeof item.chatHistory == 'string') {
                chatHistoryData = JSON.parse(item.chatHistory);
            } else {
                chatHistoryData = item.chatHistory;
            }
        }
    } catch (e) {
        console.error('解析聊天历史失败:', e);
        chatHistoryData = [];
    }

    // 设置id属性，确保与现有代码兼容
    return {
        ...item,
        id: `api_${item.id}`,
        title: item.conversationTitle || '未命名对话',
        sessionId: item.conversationId || item.id,
        apiData: item, // 保存原始API数据
        messages: chatHistoryData.map(msg => {
            const isUserMessage = msg.role === 'user' || msg.role === 'User' || msg.type === 'user';
            let messageContent = msg.content || '';
            let originalContent = messageContent;
            let processedContent = '';

            // 提取思考步骤和引用信息
            let thinkingSteps = [];
            let docAggs = [];
            let preserveThinking = true; // 默认保留思考内容

            // 尝试解析非用户消息的内容，提取思考步骤和引用
            if (!isUserMessage) {
                try {
                    // 检查是否是JSON格式
                    if (messageContent.trim().startsWith('{')) {
                        try {
                            const contentObj = JSON.parse(messageContent);

                            // 如果是我们保存的增强格式
                            if (contentObj.content !== undefined) {
                                // 保存原始内容
                                originalContent = contentObj.content;

                                // 处理思考步骤
                                if (contentObj.thinkingSteps && Array.isArray(contentObj.thinkingSteps)) {
                                    thinkingSteps = contentObj.thinkingSteps;
                                    console.log('从历史记录中恢复思考步骤，数量:', thinkingSteps.length);

                                    // 确保每个思考步骤都有完整的内容
                                    thinkingSteps = thinkingSteps.map((step, index) => {
                                        // 如果step是null或undefined，创建一个新对象
                                        if (!step) {
                                            console.warn(`历史记录中第${index + 1}个思考步骤为空，已创建新对象`);
                                            return {
                                                step: index + 1,
                                                content: '思考步骤内容',
                                                isLast: index === thinkingSteps.length - 1
                                            };
                                        }

                                        // 如果step.content为空，添加默认内容
                                        if (!step.content) {
                                            console.warn(`历史记录中第${index + 1}个思考步骤内容为空，已添加默认内容`);
                                            step.content = '思考步骤内容';
                                        }

                                        // 处理思考步骤内容，确保是纯文本而不是HTML或JSON
                                        if (step.content) {
                                            // 使用processThinkingContent函数处理内容
                                            step.content = processThinkingContent(step.content);
                                        }

                                        // 确保step有必要的属性
                                        return {
                                            ...step,
                                            step: step.step || (index + 1),
                                            isLast: index === thinkingSteps.length - 1
                                        };
                                    });
                                }

                                // 处理参考文档
                                if (contentObj.docAggs && Array.isArray(contentObj.docAggs)) {
                                    docAggs = contentObj.docAggs;
                                }

                                // 处理保留思考标志
                                if (contentObj.preserveThinking !== undefined) {
                                    preserveThinking = contentObj.preserveThinking;
                                }

                                // 使用content字段作为实际内容
                                messageContent = contentObj.content || '';
                            }
                        } catch (parseError) {
                            console.warn('JSON解析失败，使用原始内容:', parseError);
                        }
                    }

                    // 如果不是JSON格式，检查是否包含[FILELIST][/FILELIST]格式的参考文档
                    if (docAggs.length === 0) {
                        const fileListMatch = messageContent.match(/\[FILELIST\](.*?)\[\/FILELIST\]/);
                        if (fileListMatch) {
                            const fileListContent = fileListMatch[1];
                            if (fileListContent.trim()) {
                                try {
                                    // 尝试解析JSON格式的参考文档数据
                                    docAggs = JSON.parse(fileListContent);
                                    console.log('从历史记录中解析出参考文档:', docAggs);
                                } catch (e) {
                                    // 如果JSON解析失败，尝试按旧格式解析（逗号分隔的文件名）
                                    console.warn('JSON解析失败，尝试按旧格式解析:', e);
                                    const fileNames = fileListContent.split(',').map(name => name.trim()).filter(name => name);
                                    docAggs = fileNames.map((fileName, index) => ({
                                        doc_id: `file_${index}`,
                                        doc_name: fileName
                                    }));
                                    console.log('按旧格式解析出参考文档:', docAggs);
                                }
                            }
                            // 从内容中移除[FILELIST][/FILELIST]标签
                            messageContent = messageContent.replace(/\[FILELIST\].*?\[\/FILELIST\]/, '');
                            originalContent = messageContent;
                        }
                    }

                    // 尝试从HTML内容中提取思考块
                    if (thinkingSteps.length === 0) {
                        const thinkingBlockRegex = /<div class="thinking-block[^"]*"[^>]*>([\s\S]*?)<\/div>/g;
                        const extractedBlocks = [];
                        const seenContents = new Set(); // 用于去重
                        let match;
                        let stepCount = 1;

                        while ((match = thinkingBlockRegex.exec(messageContent)) !== null) {
                            // 使用processThinkingContent处理提取的内容，确保是纯文本
                            const processedContent = processThinkingContent(match[1]);

                            // 只有当内容不重复时才添加
                            if (!seenContents.has(processedContent)) {
                                seenContents.add(processedContent);
                                extractedBlocks.push({
                                    step: stepCount++,
                                    content: processedContent,
                                    isLast: false
                                });
                            }
                        }

                        if (extractedBlocks.length > 0) {
                            extractedBlocks[extractedBlocks.length - 1].isLast = true;
                            thinkingSteps = extractedBlocks;
                            console.log('从HTML内容中提取到思考块，数量:', thinkingSteps.length);
                        }
                    }

                    // 确保所有思考步骤内容都经过处理
                    if (thinkingSteps.length > 0) {
                        thinkingSteps = thinkingSteps.map(step => {
                            if (step && step.content) {
                                step.content = processThinkingContent(step.content);
                            }
                            return step;
                        });
                    }
                } catch (e) {
                    console.error('解析消息内容中的思考步骤失败:', e);
                }

                // 对系统消息使用processContent处理，确保样式正确应用
                processedContent = processContent(messageContent);
            } else {
                // 用户消息保持原样
                processedContent = messageContent;
            }

            return {
                type: isUserMessage ? 'user' : 'system',
                content: processedContent, // 使用处理后的内容，确保HTML格式和样式正确应用
                originalContent: originalContent, // 保存原始内容，用于保存历史记录
                typingCompleted: true,
                showReferences: docAggs.length > 0,
                thinkingSteps: thinkingSteps.length > 0 ? thinkingSteps : null,
                docAggs: docAggs.length > 0 ? docAggs : null,
                showThinkingSteps: true, // 默认展开思考步骤
                preserveThinking: preserveThinking // 使用提取或默认的保留思考标志
            };
        })
    };
};

// 显示Toast消息
const showToast = (message, type = 'success') => {
    // 清除现有的timeout
    if (toast.value.timeout) {
        clearTimeout(toast.value.timeout);
    }

    // 设置新的toast
    toast.value = {
        show: true,
        message,
        type,
        timeout: null
    };

    // 3秒后自动隐藏
    toast.value.timeout = setTimeout(() => {
        toast.value.show = false;
    }, 3000);
};

// 调试函数：检查消息对象中的思考时间
const checkThinkingTimes = () => {
    console.log('检查所有消息的状态:');
    messages.value.forEach((msg, index) => {
        if (msg.type === 'system' && !msg.welcome) {
            console.log(`消息[${index}]: thinking=${msg.thinking}, typingCompleted=${msg.typingCompleted}, thinkingTime=${msg.thinkingTime || '未设置'}`);
        }
    });
};

// 创建滚动标记元素的函数 - 简化版，不再实际使用
const createScrollMarker = () => {
    // 返回一个空元素，因为我们现在直接滚动聊天容器
    return document.createElement('div');
};

// 添加一个标记变量，表示是否正在切换知识库
const isSwitchingKnowledgeBase = ref(false);
// 添加一个标记变量，阻止页面初始加载时的自动滚动
const preventInitialScroll = ref(true);
// 添加一个标记变量，控制是否允许AI回复时自动滚动
const allowAutoScroll = ref(false);

// 滚动到最新消息，但保持在聊天容器内部滚动
const scrollToBottom = () => {
    // 如果正在切换知识库，不执行滚动操作
    if (isSwitchingKnowledgeBase.value) {
        console.log('正在切换知识库，不执行滚动操作');
        return;
    }

    // 如果是页面初始加载，不执行滚动操作
    if (preventInitialScroll.value) {
        console.log('页面初始加载，不执行滚动操作');
        return;
    }

    // 如果不允许AI响应时自动滚动，且不是用户主动操作引发的滚动，则不执行
    if (!allowAutoScroll.value) {
        console.log('AI响应时不允许自动滚动');
        return;
    }

    // 确保DOM已更新
    nextTick(() => {
        try {
            // 只在聊天容器内部滚动，不影响整个页面
            if (chatContainer.value) {
                // 直接滚动聊天容器到底部
                chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
                console.log('已滚动聊天容器到底部');
            }
        } catch (error) {
            console.error('滚动过程中出错:', error);
        }
    });
};

// 发送消息
const sendMessage = async () => {
    console.log('sendMessage 被调用，selectedHistoryIndex:', selectedHistoryIndex.value);
    if (!userMessage.value.trim() || isProcessing.value) return;

    // 特殊测试用例 - 用于测试思考样式
    if (userMessage.value == 'test-thinking') {
        messages.value.push({ type: 'user', content: '测试思考样式' });

        // 添加系统响应
        const thinkingStyle = 'display:block; background-color:#f5f5f5; color:#666; padding:8px 10px; margin:4px 0; border-radius:4px;';
        const testContent = `<span style="${thinkingStyle}">思考内容，浅灰色样式</span>实际回答，正常黑色样式`;

        messages.value.push({
            type: 'system',
            content: testContent,
            thinking: false
        });

        userMessage.value = '';
        await scrollToBottom();
        return;
    }

    isProcessing.value = true;

    // 重置参考文献和参考文档
    latestReferenceChunks.value = [];
    latestDocAggs.value = [];

    // 添加用户消息
    const userContent = userMessage.value;
    const originalUserInput = userMessage.value; // 保存原始用户输入，不会被污染
    messages.value.push({
        type: 'user',
        content: userContent,
        typingCompleted: true, // 用户消息总是完成的
        responseCompleted: true // 用户消息总是完成的
    });
    console.log('用户消息已添加到messages数组:', messages.value);
    const question = userMessage.value;
    userMessage.value = '';

    // 临时允许滚动，确保发送消息时可以滚动到底部
    const originalPreventScrollValue = preventInitialScroll.value;
    const originalAllowAutoScrollValue = allowAutoScroll.value;
    preventInitialScroll.value = false;
    allowAutoScroll.value = true; // 用户发消息时允许滚动

    // 滚动到最新消息
    await scrollToBottom();

    // 恢复原始设置
    preventInitialScroll.value = originalPreventScrollValue;
    allowAutoScroll.value = originalAllowAutoScrollValue;

    // 获取历史消息的数量，用于判断是否是第一次对话
    const userMessagesCount = messages.value.filter(msg => msg.type === 'user').length;
    console.log('当前用户消息数量:', userMessagesCount);

    // 检查是否是第一次对话（不包括当前正在发送的消息）
    // 无论是日常问答还是i暖城问数模式，都需要在第一条消息时调用addHistory
    const isFirstMessage = userMessagesCount === 1; // 只有当前这一条
    console.log('是否是第一次对话:', isFirstMessage, '当前模式:', selectedMode.value.value);

    try {
        // 获取本地存储的会话ID - 提前声明，避免ReferenceError
        const savedConversationId = localStorage.getItem('current_conversation_id');

        // 检查是否正在查看历史记录
        if (selectedHistoryIndex.value !== -1) {
            console.log('正在查看历史记录，需要开启新会话，selectedHistoryIndex:', selectedHistoryIndex.value);
            // 获取当前查看的历史记录
            let currentHistory = null;
            if (chatHistory.value.recent7Days) {
                currentHistory = chatHistory.value.recent7Days.find(h => getHistoryFullIndex(h) === selectedHistoryIndex.value);
            }
            if (!currentHistory && chatHistory.value.recent30Days) {
                currentHistory = chatHistory.value.recent30Days.find(h => getHistoryFullIndex(h) === selectedHistoryIndex.value);
            }
            if (!currentHistory && chatHistory.value.before30Days) {
                currentHistory = chatHistory.value.before30Days.find(h => getHistoryFullIndex(h) === selectedHistoryIndex.value);
            }

            if (currentHistory && currentHistory.apiData) {
                console.log('找到历史记录的apiData字段');

                // 保存当前消息列表，避免被清空
                const savedMessages = [...messages.value];

                // 获取新的session_id，但不使用createNewSession()，避免清空历史消息
                try {
                    console.log('获取新的会话ID，但不清空历史消息 - 连续对话模式');

                    // 准备发送的数据
                    let requestData = {
                        "name": "连续对话",
                        "role": "user",
                        "env": selectedMode.value.value === 'WORK_REPORT' ? ENV_BG : ENV
                    };

                    // 处理kbId参数
                    if (selectedMode.value.value === 'WORK_REPORT') {
                        // 创城工作报告模式使用null
                        requestData.kbId = null;
                        // 添加deptId参数
                        requestData.deptId = userStore.users?.deptId || localStorage.getItem('deptId');
                        console.log('创城工作报告模式（连续对话），使用kbId: null, deptId:', requestData.deptId);
                    } else if (selectedMode.value.value === 'content_creation') {
                        // 内容创作模式添加type参数，根据联网状态决定
                        requestData.type = isNetworkEnabled.value ? 'intel' : 'local';
                        let kbId = null;
                        if (repoIds.value) {
                            const selectedRepo = repoList.value.find(repo => repo.repoId == repoIds.value);
                            if (selectedRepo) {
                                kbId = selectedRepo.isJoined ? selectedRepo.repoId : (selectedRepo.sharedRepoId || selectedRepo.repoId);
                            } else {
                                kbId = repoIds.value;
                            }
                        } else if (selectedKnowledgeBase.value) {
                            if (selectedKnowledgeBase.value.value === 'unit') {
                                kbId = null;
                            } else if (selectedKnowledgeBase.value.value.startsWith('repo_')) {
                                const selectedRepoId = selectedKnowledgeBase.value.value.split('_')[1];
                                const selectedRepo = repoList.value.find(repo => repo.repoId == selectedRepoId);
                                if (selectedRepo) {
                                    kbId = selectedRepo.isJoined ? selectedRepo.repoId : (selectedRepo.sharedRepoId || selectedRepo.repoId);
                                } else {
                                    kbId = selectedRepoId;
                                }
                            }
                        }
                        requestData.kbId = kbId ? String(kbId) : null;
                        console.log('内容创作模式（连续对话），使用kbId:', requestData.kbId, 'type:', requestData.type);
                    } else {
                        // 其他模式的kbId处理
                        let kbId = null;
                        if (repoIds.value) {
                            const selectedRepo = repoList.value.find(repo => repo.repoId == repoIds.value);
                            if (selectedRepo) {
                                kbId = selectedRepo.isJoined ? selectedRepo.repoId : (selectedRepo.sharedRepoId || selectedRepo.repoId);
                            } else {
                                kbId = repoIds.value;
                            }
                        } else if (selectedKnowledgeBase.value) {
                            if (selectedKnowledgeBase.value.value === 'unit') {
                                kbId = null;
                            } else if (selectedKnowledgeBase.value.value.startsWith('repo_')) {
                                const selectedRepoId = selectedKnowledgeBase.value.value.split('_')[1];
                                const selectedRepo = repoList.value.find(repo => repo.repoId == selectedRepoId);
                                if (selectedRepo) {
                                    kbId = selectedRepo.isJoined ? selectedRepo.repoId : (selectedRepo.sharedRepoId || selectedRepo.repoId);
                                } else {
                                    kbId = selectedRepoId;
                                }
                            }
                        }
                        requestData.kbId = kbId ? String(kbId) : null;
                        console.log('其他模式（连续对话），使用kbId:', requestData.kbId);
                    }

                    // 根据当前选择的模式决定使用哪个API端点
                    let agentId;
                    switch (selectedMode.value.value) {
                        case 'v2':
                            agentId = AGENT_ID;
                            break;
                        case 'content_creation':
                            agentId = AGENT_ID_NR;
                            break;
                        case 'policy_qa':
                            agentId = AGENT_ID_ZC;
                            break;
                        case 'WORK_REPORT':
                            agentId = AGENT_ID_BG;
                            break;
                        case 'r1':
                        default:
                            agentId = CHAT_ID;
                            break;
                    }
                    const url = `${getApiBaseUrl()}/api/v1/agents/${agentId}/sessions`;

                    const response = await axios({
                        method: 'post',
                        url: url,
                        headers: {
                            'Authorization': getApiToken(),
                            'Content-Type': 'application/json',
                        },
                        data: JSON.stringify(requestData),
                        timeout: 10000
                    });

                    if (response.status === 200) {
                        // 获取新的会话ID
                        const newSessionId = response.data.data?.id || '';
                        if (newSessionId) {
                            sessionId.value = newSessionId;
                            console.log('成功获取新会话ID:', sessionId.value);
                        } else {
                            console.error('获取新会话ID失败');
                        }
                    } else {
                        console.error('创建会话请求失败:', response.status);
                    }
                } catch (error) {
                    console.error('获取新会话ID出错:', error);
                }

                // 恢复保存的消息列表
                messages.value = savedMessages;

                // 处理历史记录的chatHistory字段
                let historyContent = '';
                const chatHistoryData = currentHistory.apiData.chatHistory;

                if (chatHistoryData) {
                    // 如果chatHistory是字符串，直接使用
                    if (typeof chatHistoryData === 'string') {
                        historyContent = chatHistoryData;
                        console.log('chatHistory是字符串类型');
                    }
                    // 如果是数组或对象，转换为字符串
                    else if (typeof chatHistoryData === 'object') {
                        try {
                            historyContent = JSON.stringify(chatHistoryData);
                            console.log('chatHistory是对象类型，已转换为字符串');
                        } catch (e) {
                            console.error('转换chatHistory为字符串失败:', e);
                            historyContent = '';
                        }
                    }
                } else {
                    console.log('历史记录中没有chatHistory字段，创建新的连续对话');
                    // 即使没有chatHistory字段，我们也继续进行连续对话
                }

                // 拼接历史记录的chatHistory和用户新发送的消息，但只用于API请求，不显示在页面上
                const combinedQuestion = historyContent ? `${historyContent}\n${originalUserInput}` : originalUserInput;
                console.log('拼接后的问题:', historyContent ? '历史内容 + 新问题' : '仅新问题');

                // 调用completions接口获取AI回复
                console.log('调用completions接口获取AI回复，使用会话ID:', sessionId.value);

                // 在调用API前，先移除拼接的消息，只保留用户实际输入的消息
                // 查找并移除最后一条用户消息（如果是拼接的消息）
                const lastUserMsgIndex = messages.value.length - 1;
                if (lastUserMsgIndex >= 0 && messages.value[lastUserMsgIndex].type === 'user') {
                    // 确保显示的是用户原始输入，而不是拼接后的内容
                    messages.value[lastUserMsgIndex].content = originalUserInput;
                }

                // 历史记录模式下：用户一发送消息就立即保存用户消息并查询历史记录列表
                console.log('历史记录模式下：用户发送消息，立即保存用户消息');

                // 获取历史记录ID
                const historyId = savedConversationId || (currentHistory.apiData && currentHistory.apiData.id);

                if (historyId) {
                    try {
                        // 立即保存用户消息
                        console.log('历史记录模式：立即调用addMessage保存用户消息，参数:', {
                            id: historyId,
                            conversationId: sessionId.value,
                            content: originalUserInput,
                            role: 'user'
                        });

                        // 处理内容转义
                        let contentToSave = originalUserInput;
                        if (selectedMode.value.value === 'content_creation' && isNetworkEnabled.value) {
                            contentToSave = escapeHtmlContent(originalUserInput);
                            console.log('内容创作联网模式：对用户消息进行HTML转义处理');
                        }

                        await addMessage({
                            id: historyId,
                            conversationId: sessionId.value,
                            content: contentToSave,
                            role: 'user',
                            repoId: repoIds.value // 添加 repoId 参数
                        });
                        console.log('历史记录模式：用户消息保存成功');

                        // 立即更新选中状态（历史记录模式）
                        setSelectedHistoryImmediately(historyId, true);

                        // 用户消息保存后延迟刷新历史记录列表
                        try {
                            console.log('历史记录模式：用户消息保存后延迟刷新历史记录列表');
                            // 添加延迟确保数据库已更新
                            await new Promise(resolve => setTimeout(resolve, 300));
                            await fetchHistoryFromAPI();
                            console.log('历史记录模式：历史记录列表已刷新（用户消息保存后）');

                            // 历史记录刷新后，立即选中对应的历史记录（应该在第一个位置）
                            selectNewlyCreatedHistory(historyId);
                        } catch (error) {
                            console.error('历史记录模式：用户消息保存后刷新历史记录列表失败:', error);
                        }
                    } catch (error) {
                        console.error('历史记录模式：保存用户消息失败:', error);
                    }
                } else {
                    console.error('历史记录模式：未找到有效的历史记录ID，无法保存用户消息');
                }

                // 发送实际拼接的问题到API，但传递的参数是combinedQuestion，而不是question
                const response = await sendMessageToAPI(combinedQuestion);
                console.log('AI回复完成');

                // AI回复完成后，保存机器人回复
                if (historyId) {
                    try {

                        // 获取最后一条系统消息作为机器人回复
                        const systemMessages = messages.value.filter(msg =>
                            msg.type === 'system' && // 是系统消息
                            !msg.welcome &&         // 不是欢迎消息
                            !msg.thinking           // 不是思考中状态
                        );

                        if (systemMessages.length > 0) {
                            const lastSystemMessage = systemMessages[systemMessages.length - 1];

                            // 获取原始内容
                            let rawContent = '';

                            if (lastSystemMessage.originalContent) {
                                rawContent = lastSystemMessage.originalContent;
                            } else if (lastSystemMessage.content) {
                                rawContent = lastSystemMessage.content;
                            }

                            // 保存机器人回复
                            if (rawContent.trim()) {
                                console.log('准备调用addMessage保存机器人回复');

                                // 获取原始内容
                                let contentToSave = lastSystemMessage.originalContent || rawContent;

                                // 将所有思考标签转换为[THINKING]标签，用于历史记录保存
                                if (contentToSave.includes('<think>') || contentToSave.includes('<thinking>')) {
                                    contentToSave = contentToSave
                                        .replace(/<think>/g, '[THINKING]')
                                        .replace(/<\/think>/g, '[/THINKING]')
                                        .replace(/<thinking>/g, '[THINKING]')
                                        .replace(/<\/thinking>/g, '[/THINKING]');
                                    console.log('历史记录模式：已将思考标签转换为[THINKING]标签用于历史记录保存');
                                }

                                // 获取参考文档数据并拼接到内容后面
                                const docAggs = lastSystemMessage.docAggs || [];
                                if (docAggs.length > 0) {
                                    const fileListContent = JSON.stringify(docAggs);
                                    contentToSave += `[FILELIST]${fileListContent}[/FILELIST]`;
                                    console.log('历史记录模式：已将参考文档拼接到内容后面:', docAggs);
                                }

                                // 处理内容转义
                                let finalContentToSave = contentToSave;
                                if (selectedMode.value.value === 'content_creation' && isNetworkEnabled.value) {
                                    finalContentToSave = escapeHtmlContent(contentToSave);
                                    console.log('内容创作联网模式：对机器人回复进行HTML转义处理');
                                }

                                await addMessage({
                                    id: historyId,
                                    conversationId: sessionId.value,
                                    content: finalContentToSave,
                                    role: 'assistant',
                                    repoId: repoIds.value // 添加 repoId 参数
                                });
                                console.log('历史记录模式：机器人回复保存成功');

                                // 机器人回复保存后延迟刷新历史记录列表
                                try {
                                    console.log('历史记录模式：机器人回复保存后延迟刷新历史记录列表');
                                    // 添加延迟确保数据库已更新
                                    await new Promise(resolve => setTimeout(resolve, 300));
                                    await fetchHistoryFromAPI();
                                    console.log('历史记录模式：历史记录列表已刷新（机器人回复保存后）');

                                    // 历史记录刷新后，立即选中对应的历史记录（应该在第一个位置）
                                    selectNewlyCreatedHistory(historyId);
                                } catch (error) {
                                    console.error('历史记录模式：机器人回复保存后刷新历史记录列表失败:', error);
                                }
                            }
                        }
                    } catch (error) {
                        console.error('保存消息失败:', error);
                    }
                }

                // 更新选中的历史记录索引
                if (currentHistory && currentHistory.apiData) {
                    const updatedIndex = getHistoryFullIndex(currentHistory);
                    if (updatedIndex !== -1) {
                        selectedHistoryIndex.value = updatedIndex;
                        console.log('历史记录模式：已更新选中的历史记录索引:', updatedIndex);
                    } else {
                        // 如果找不到历史记录，尝试通过ID重新查找
                        const allHistories = [
                            ...chatHistory.value.recent7Days,
                            ...chatHistory.value.recent30Days,
                            ...chatHistory.value.before30Days
                        ];
                        const foundHistory = allHistories.find(h => h.id === currentHistory.id);
                        if (foundHistory) {
                            const newIndex = getHistoryFullIndex(foundHistory);
                            selectedHistoryIndex.value = newIndex;
                            console.log('历史记录模式：通过ID重新找到历史记录，更新索引:', newIndex);
                        }
                    }
                }

                // 历史记录模式下的消息已经保存，这里不需要重复保存

                // 历史记录模式下已经发送了API请求，不需要再发送一次
                console.log('历史记录模式下消息发送完成，已更新选中状态');
                // 这里添加return，防止代码继续执行到下面的sendMessageToAPI
                return;
            } else {
                console.log('未找到历史记录的chatHistory字段或历史记录不存在');
                // 虽然没有chatHistory字段，但仍然是历史记录模式
                // 这种情况下，我们应该像普通对话一样处理，但仍然需要设置历史记录ID
            }
        }

        // 仅当没有会话ID时才创建新会话
        if (!sessionId.value) {
            console.log('没有会话ID，创建新会话');
            await createSession();
            console.log('创建会话完成，会话ID:', sessionId.value);
        } else {
            console.log('继续使用现有会话ID:', sessionId.value);
        }

        // 先创建历史记录，再调用completions接口
        console.log('准备创建历史记录');

        // 定义变量保存会话ID
        let chatId = null;

        // 获取当前选中的知识库ID
        let currentRepoId = null;

        // 首先尝试从左侧菜单选择的知识库获取ID
        if (repoIds.value) {
            currentRepoId = repoIds.value;
            console.log('保存历史：使用左侧菜单选择的知识库ID:', currentRepoId);
        }
        // 如果是单位知识库模式，使用null
        else if (selectedKnowledgeBase.value && selectedKnowledgeBase.value.value === 'unit') {
            currentRepoId = null;
            console.log('保存历史：单位知识库使用null作为ID');
        }
        // 如果是特定知识库，提取ID
        else if (selectedKnowledgeBase.value && selectedKnowledgeBase.value.value.startsWith('repo_')) {
            currentRepoId = parseInt(selectedKnowledgeBase.value.value.split('_')[1]);
            console.log('保存历史：使用特定知识库ID:', currentRepoId);
        }

        console.log('当前选中的知识库ID (保存历史):', currentRepoId);

        // 检查是否正在查看历史记录
        // 如果是查看历史记录，则不调用addHistory接口，但需要调用addMessage接口
        if (selectedHistoryIndex.value && selectedHistoryIndex.value !== -1) {
            // 直接从localStorage获取历史记录ID
            chatId = savedConversationId;
            console.log('正在查看历史记录，使用历史记录ID:', chatId);
        }
        // 只有在不是查看历史记录，且是第一次对话或没有保存的会话ID时才调用addHistory接口
        else if (isFirstMessage || !savedConversationId) {
            try {
                console.log('准备调用addHistory接口，参数:', {
                    repoId: currentRepoId,
                    title: userContent
                });

                // 根据当前模式设置conversationType
                const conversationType = getConversationType(selectedMode.value.value);

                // 在发送请求前打印当前模式信息以便调试
                console.log('发送addHistory请求前的模式信息:');
                console.log('selectedMode完整对象:', selectedMode.value);
                console.log('selectedMode.value属性:', selectedMode.value.value);
                console.log('将要发送的conversationType:', conversationType);

                const historyResponse = await addHistory({
                    repoId: currentRepoId,
                    title: userContent,
                    conversationType: conversationType,
                    conversationId: sessionId.value // 添加会话ID
                });

                console.log('addHistory接口响应:', historyResponse);

                // 从响应中获取历史记录ID
                if (historyResponse && historyResponse.data) {
                    chatId = historyResponse.data;
                    // 保存到localStorage
                    localStorage.setItem('current_conversation_id', chatId);
                    console.log('创建历史记录成功，历史记录ID:', chatId);

                    // 立即刷新历史记录列表，让新创建的历史记录显示在左侧
                    try {
                        console.log('历史记录创建完成，立即刷新历史记录列表');
                        await fetchHistoryFromAPI();
                        console.log('历史记录列表已刷新');

                        // 刷新后自动选中新创建的历史记录
                        selectNewlyCreatedHistory(chatId);
                    } catch (error) {
                        console.error('刷新历史记录列表失败:', error);
                    }
                }
            } catch (error) {
                console.error('创建历史记录失败:', error);
            }
        } else {
            // 如果不是第一次对话，直接使用保存的会话ID
            chatId = savedConversationId;
            console.log('非首次对话，使用已保存的历史记录ID:', chatId);
        }

        console.log('提取的会话ID (chat_id):', chatId);

        // 现在调用completions接口获取AI回复
        console.log('调用completions接口获取AI回复，使用会话ID:', sessionId.value);
        const response = await sendMessageToAPI(question);
        console.log('AI回复完成');

        if (chatId) {
            // 保存用户消息
            console.log('正常模式：准备调用addMessage保存用户消息，selectedHistoryIndex:', selectedHistoryIndex.value);
            console.log('准备调用addMessage保存用户消息，参数:', {
                id: chatId,
                conversationId: sessionId.value,
                content: userContent,
                role: 'user'
            });

            // 处理内容转义
            let contentToSave = userContent;
            if (selectedMode.value.value === 'content_creation' && isNetworkEnabled.value) {
                contentToSave = escapeHtmlContent(userContent);
                console.log('内容创作联网模式：对用户消息进行HTML转义处理');
            }

            const userMessageResponse = await addMessage({
                id: chatId, // 使用历史记录ID
                conversationId: sessionId.value, // 使用会话ID
                content: contentToSave,
                role: 'user',
                repoId: currentRepoId // 添加 repoId 参数
            });
            console.log('用户消息保存响应:', userMessageResponse);

            // 立即更新选中状态 - 判断是否为历史记录模式
            const isHistoryView = selectedHistoryIndex.value !== -1;
            setSelectedHistoryImmediately(chatId, isHistoryView);

            // 用户消息保存后延迟刷新历史记录列表
            try {
                console.log('正常模式：用户消息保存后延迟刷新历史记录列表');
                // 添加延迟确保数据库已更新
                await new Promise(resolve => setTimeout(resolve, 300));
                await fetchHistoryFromAPI();
                console.log('历史记录列表已刷新（正常模式用户消息保存后）');
            } catch (error) {
                console.error('正常模式用户消息保存后刷新历史记录列表失败:', error);
            }

            // 获取最后一条系统消息作为机器人回复
            // 过滤掉欢迎消息和思考中状态的消息，只获取用户发送后机器人的实际回复消息
            const systemMessages = messages.value.filter(msg =>
                msg.type === 'system' && // 是系统消息
                !msg.welcome &&         // 不是欢迎消息
                !msg.thinking           // 不是思考中状态
            );
            console.log('找到的系统消息数量:', systemMessages.length);

            if (systemMessages.length > 0) {
                const lastSystemMessage = systemMessages[systemMessages.length - 1];
                console.log('找到最后一条系统消息');

                // 获取原始内容
                let rawContent = '';

                // 判断是否有内容
                if (lastSystemMessage.content) {
                    // 直接使用HTML内容，保留所有样式
                    rawContent = lastSystemMessage.content;
                }

                // 保存机器人回复
                if (rawContent.trim()) {
                    console.log('准备调用addMessage保存机器人回复');

                    // 保存原始内容，但将<think>标签转换为[THINKING]标签以避免被HTML过滤
                    let contentToSave = lastSystemMessage.originalContent || rawContent;

                    // 将所有思考标签转换为[THINKING]标签，用于历史记录保存
                    if (contentToSave.includes('<think>') || contentToSave.includes('<thinking>')) {
                        contentToSave = contentToSave
                            .replace(/<think>/g, '[THINKING]')
                            .replace(/<\/think>/g, '[/THINKING]')
                            .replace(/<thinking>/g, '[THINKING]')
                            .replace(/<\/thinking>/g, '[/THINKING]');
                        console.log('已将思考标签转换为[THINKING]标签用于历史记录保存');
                    }

                    // 获取参考文档数据并拼接到内容后面
                    const docAggs = lastSystemMessage.docAggs || [];
                    if (docAggs.length > 0) {
                        const fileListContent = JSON.stringify(docAggs);
                        contentToSave += `[FILELIST]${fileListContent}[/FILELIST]`;
                        console.log('已将参考文档拼接到内容后面:', docAggs);
                    }

                    console.log('保存到历史记录的内容:', contentToSave);
                    console.log('是否包含[THINKING]标签:', contentToSave.includes('[THINKING]'));
                    console.log('是否包含[FILELIST]标签:', contentToSave.includes('[FILELIST]'));

                    // 处理内容转义
                    let finalContentToSave = contentToSave;
                    if (selectedMode.value.value === 'content_creation' && isNetworkEnabled.value) {
                        finalContentToSave = escapeHtmlContent(contentToSave);
                        console.log('内容创作联网模式：对机器人回复进行HTML转义处理');
                    }

                    // 保存完整内容到历史记录
                    console.log('思考部分输出完成，现在调用addMessage保存机器人回复');
                    const assistantMessageResponse = await addMessage({
                        id: chatId, // 使用历史记录ID
                        conversationId: sessionId.value, // 使用会话ID
                        content: finalContentToSave, // 使用完整内容对象的JSON字符串
                        role: 'assistant',
                        repoId: currentRepoId // 添加 repoId 参数
                    });
                    console.log('机器人回复保存响应:', assistantMessageResponse);
                } else {
                    console.error('机器人回复内容为空，跳过保存');
                }
            } else {
                console.error('未找到系统回复消息');

                // 最后一次尝试：获取最后一个消息
                const lastIndex = messages.value.length - 1;
                if (lastIndex >= 0 && messages.value[lastIndex].type === 'system' && !messages.value[lastIndex].thinking) {
                    const lastMsg = messages.value[lastIndex];
                    console.log('尝试使用最后一条消息');

                    // 获取内容和其他重要数据
                    const rawContent = lastMsg.content || '';
                    const thinkingSteps = lastMsg.thinkingSteps || [];
                    const docAggs = lastMsg.docAggs || [];

                    if (rawContent) {
                        console.log('使用最后一条消息保存机器人回复');

                        // 重新获取最新的数据
                        const updatedThinkingSteps = lastMsg.thinkingSteps || [];
                        const updatedDocAggs = lastMsg.docAggs || [];

                        // 创建完整的保存内容对象，包含思考步骤和参考文档
                        const completeContent = {
                            content: lastMsg.originalContent || rawContent,
                            thinkingSteps: updatedThinkingSteps,
                            docAggs: updatedDocAggs,
                            preserveThinking: true
                        };

                        // 转换为JSON字符串
                        const contentToSave = JSON.stringify(completeContent);
                        console.log('保存的完整内容对象:', completeContent);

                        // 处理内容转义
                        let finalContentToSave = contentToSave;
                        if (selectedMode.value.value === 'content_creation' && isNetworkEnabled.value) {
                            finalContentToSave = escapeHtmlContent(contentToSave);
                            console.log('内容创作联网模式：对机器人回复进行HTML转义处理');
                        }

                        console.log('思考部分输出完成，现在调用addMessage保存机器人回复');
                        await addMessage({
                            id: chatId, // 使用历史记录ID
                            conversationId: sessionId.value, // 使用会话ID
                            content: finalContentToSave, // 使用完整内容对象的JSON字符串
                            role: 'assistant',
                            repoId: currentRepoId // 添加 repoId 参数
                        });
                    }
                }
            }
        } else {
            console.error('未获取到有效的chat_id，无法保存消息');
        }
    } catch (error) {
        console.error('保存对话历史失败，错误详情:', error);
    }

    // 更新历史记录列表，但不更新当前显示的消息
    try {
        console.log('发送消息后开始更新历史记录列表，但保留当前思考步骤');

        // 保存当前消息中的思考步骤
        const savedMessages = [...messages.value];
        const messagesWithThinking = savedMessages.filter(msg =>
            msg.type === 'system' && msg.thinkingSteps && msg.thinkingSteps.length > 0
        );

        if (messagesWithThinking.length > 0) {
            console.log(`保存了${messagesWithThinking.length}条带思考步骤的消息`);
        }

        // 更新历史记录，但不改变当前messages
        try {
            const historyResponse = await getMessage();

            if (historyResponse.code == 0 && historyResponse.data) {
                console.log('历史记录API返回成功，但不更新当前消息列表');

                // 保存当前选中的历史记录ID，用于更新后重新定位
                let currentHistoryId = null;
                if (selectedHistoryIndex.value !== -1) {
                    const allHistories = [
                        ...chatHistory.value.recent7Days,
                        ...chatHistory.value.recent30Days,
                        ...chatHistory.value.before30Days
                    ];
                    const currentHistory = allHistories.find(h => getHistoryFullIndex(h) === selectedHistoryIndex.value);
                    if (currentHistory) {
                        currentHistoryId = currentHistory.id;
                    }
                }

                // 更新chatHistory，但不影响当前messages
                chatHistory.value = {
                    recent7Days: (historyResponse.data.recent7Days || []).map(enrichHistoryItem),
                    recent30Days: (historyResponse.data.recent30Days || []).map(enrichHistoryItem),
                    before30Days: (historyResponse.data.before30Days || []).map(enrichHistoryItem)
                };

                // 如果之前有选中的历史记录，重新计算其索引
                if (currentHistoryId) {
                    const allUpdatedHistories = [
                        ...chatHistory.value.recent7Days,
                        ...chatHistory.value.recent30Days,
                        ...chatHistory.value.before30Days
                    ];
                    const updatedHistory = allUpdatedHistories.find(h => h.id === currentHistoryId);
                    if (updatedHistory) {
                        const newIndex = getHistoryFullIndex(updatedHistory);
                        selectedHistoryIndex.value = newIndex;
                        console.log('已更新选中历史记录的索引:', newIndex);
                    }
                }
            }
        } catch (fetchError) {
            console.error('后台更新历史记录失败:', fetchError);
        }

        console.log('发送消息后已更新历史记录列表（仅后台）');
    } catch (error) {
        console.error('更新历史记录列表失败:', error);
    }

    // 只有在历史记录模式下，且已经完成所有操作后，才重置历史记录索引
    // 这里不需要重置，因为在历史记录模式下，我们已经提前return了

    // 确保在函数结束时总是重置处理状态，无论之前是否已经重置
    isProcessing.value = false;
    console.log('sendMessage函数结束，已重置处理状态');
};

// 处理建议问题点击 - 与发送消息逻辑保持一致
const askSuggestedQuestion = async (question) => {
    if (isProcessing.value) return;

    console.log('建议问题被点击:', question);
    isProcessing.value = true;

    // 设置允许自动滚动标志
    allowAutoScroll.value = true;

    try {
        // 重置参考文献和参考文档
        latestReferenceChunks.value = [];
        latestDocAggs.value = [];

        // 检查是否是i暖城问数模式且从历史记录加载
        const isDataQueryMode = selectedMode.value && selectedMode.value.value === 'v2';
        const isHistoryView = selectedHistoryIndex.value !== -1;

        // 如果是i暖城问数模式且从历史记录加载，需要先创建新会话
        if (isDataQueryMode && isHistoryView) {
            console.log('i暖城问数模式下点击快捷词，需要创建新会话');

            // 保存旧的会话ID，以便后续使用
            const oldSessionId = sessionId.value;
            const oldHistoryId = localStorage.getItem('current_conversation_id');

            // 创建新会话，但不清除消息列表
            const sessionCreated = await createSession(false);
            if (!sessionCreated) {
                console.error('创建新会话失败');
                isProcessing.value = false;
                return;
            }

            console.log('成功创建新会话，旧会话ID:', oldSessionId, '新会话ID:', sessionId.value);
            console.log('保留旧的历史记录ID:', oldHistoryId);

            // 恢复历史记录ID，这样后续的addMessage会添加到正确的历史记录中
            if (oldHistoryId) {
                localStorage.setItem('current_conversation_id', oldHistoryId);
            }

            // 注意：这里不重置历史索引，保持当前选中状态
            // 因为用户在当前对话中点击快捷词，应该继续在当前对话中
            console.log('i暖城问数模式下点击快捷词，保持当前历史记录选中状态');
            console.log('创建新会话后，当前选中状态:', selectedHistoryIndex.value);
        }

        // 创建用户消息对象 - 确保保留用户问题
        const userMessage = {
            type: 'user',
            content: question,
            id: Date.now().toString(), // 添加唯一ID以便追踪
            typingCompleted: true, // 用户消息总是完成的
            responseCompleted: true // 用户消息总是完成的
        };

        // 先保存原始消息列表长度
        const originalMessagesLength = messages.value.length;

        // 添加用户消息
        messages.value.push(userMessage);
        console.log('用户建议问题消息已添加到messages数组:', messages.value);

        // 滚动到最新消息
        await scrollToBottom();

        // 如果没有会话ID且不是i暖城问数模式的历史记录查看，则创建会话
        if (!sessionId.value && !(isDataQueryMode && isHistoryView)) {
            console.log('建议问题：没有会话ID，需要创建新会话');
            const sessionCreated = await createSession();
            if (!sessionCreated) {
                isProcessing.value = false;
                return;
            }
        } else {
            console.log('建议问题：使用现有会话ID:', sessionId.value);
        }

        // 验证用户消息是否还存在，如果被删除则重新添加
        const userMessageExists = messages.value.some(msg =>
            msg.type === 'user' && msg.content === question
        );

        if (!userMessageExists) {
            console.log('用户消息被移除，重新添加');
            messages.value.push(userMessage);
            await scrollToBottom();
        }

        // 添加思考中状态消息
        const thinkingMessage = {
            type: 'system',
            content: '思考中...',
            thinking: true,
            typingCompleted: false,
            showReferences: false,
            thinkingTime: Date.now(), // 记录开始思考的时间
            preserveThinking: true // 确保思考步骤被保留
        };

        messages.value.push(thinkingMessage);
        await scrollToBottom();

        // 先处理对话历史，再调用API
        console.log('准备保存对话历史');

        // 处理对话历史
        try {
            // 先检查localStorage中是否已有会话ID
            const savedConversationId = localStorage.getItem('current_conversation_id');

            // 获取当前选中的知识库ID
            let currentRepoId = null;

            // 首先尝试从左侧菜单选择的知识库获取ID
            if (repoIds.value) {
                currentRepoId = repoIds.value;
                console.log('建议问题：使用左侧菜单选择的知识库ID:', currentRepoId);
            }
            // 如果是单位知识库模式，使用null
            else if (selectedKnowledgeBase.value && selectedKnowledgeBase.value.value === 'unit') {
                currentRepoId = null;
                console.log('建议问题：单位知识库使用null作为ID');
            }
            // 如果是特定知识库，提取ID
            else if (selectedKnowledgeBase.value && selectedKnowledgeBase.value.value.startsWith('repo_')) {
                currentRepoId = parseInt(selectedKnowledgeBase.value.value.split('_')[1]);
                console.log('建议问题：使用特定知识库ID:', currentRepoId);
            }

            console.log('当前选中的知识库ID(建议问题):', currentRepoId);

            // 定义变量保存会话ID
            let chatId = null;

            // 检查是否已有保存的会话ID
            if (savedConversationId) {
                // 如果已有会话ID，直接使用
                chatId = savedConversationId;
                console.log('建议问题：使用已保存的历史记录ID:', chatId);

                // 检查是否是i暖城问数模式且从历史记录加载
                const isDataQueryMode = selectedMode.value && selectedMode.value.value === 'v2';
                const isHistoryView = selectedHistoryIndex.value !== null;

                if (isDataQueryMode && isHistoryView) {
                    console.log('i暖城问数模式下历史记录点击快捷词，不需要创建新的历史记录');
                }
            } else {
                // 如果没有会话ID，才创建新的历史记录
                // 根据当前模式设置conversationType
                const conversationType = getConversationType(selectedMode.value.value);

                console.log('建议问题：发送addHistory请求前的模式信息:');
                console.log('selectedMode完整对象:', selectedMode.value);
                console.log('selectedMode.value属性:', selectedMode.value.value);
                console.log('将要发送的conversationType:', conversationType);

                const historyResponse = await addHistory({
                    repoId: currentRepoId,
                    title: question,
                    conversationType: conversationType,
                    conversationId: sessionId.value // 添加会话ID
                });

                console.log('addHistory接口响应:', historyResponse);

                // 从响应中获取新会话ID
                if (historyResponse && historyResponse.data) {
                    chatId = historyResponse.data;
                    // 保存到localStorage
                    localStorage.setItem('current_conversation_id', chatId);
                    console.log('已创建历史记录，ID:', chatId);

                    // 立即刷新历史记录列表，让新创建的历史记录显示在左侧
                    try {
                        console.log('快捷词首次对话完成，立即刷新历史记录列表');
                        await fetchHistoryFromAPI();
                        console.log('历史记录列表已刷新');

                        // 刷新后直接设置选中状态为第一个位置
                        selectedHistoryIndex.value = 0;
                        console.log('快捷词首次对话，直接设置选中状态为第一个位置:', selectedHistoryIndex.value);
                    } catch (error) {
                        console.error('刷新历史记录列表失败:', error);
                    }
                } else {
                    console.error('创建历史记录失败且没有已保存的会话ID');
                }
            }

            console.log('使用的会话ID (chat_id):', chatId);

            // 快捷词：用户点击快捷词后立即保存用户消息并查询历史记录列表
            if (chatId) {
                // 立即保存用户消息
                console.log('快捷词：用户点击快捷词，立即保存用户消息');

                // 处理内容转义
                let contentToSave = question;
                if (selectedMode.value.value === 'content_creation' && isNetworkEnabled.value) {
                    contentToSave = escapeHtmlContent(question);
                    console.log('内容创作联网模式：对快捷词进行HTML转义处理');
                }

                await addMessage({
                    id: chatId, // 使用历史记录ID
                    conversationId: sessionId.value, // 使用会话ID
                    content: contentToSave,
                    role: 'user',
                    repoId: currentRepoId // 添加 repoId 参数
                });

                // 立即更新选中状态 - 快捷词点击后总是选中第一个位置
                // 因为新消息会让历史记录移动到列表顶部，无论之前是否在历史记录模式
                console.log('快捷词点击，准备设置选中状态，当前状态:', selectedHistoryIndex.value);
                selectedHistoryIndex.value = 0; // 直接设置为0，确保选中第一个位置
                console.log('快捷词点击，选中状态已更新为第一个位置，当前状态:', selectedHistoryIndex.value);

                // 用户消息保存后延迟刷新历史记录列表
                try {
                    console.log('快捷词：用户消息保存后延迟刷新历史记录列表');
                    // 添加延迟确保数据库已更新
                    await new Promise(resolve => setTimeout(resolve, 300));
                    await fetchHistoryFromAPI();
                    console.log('历史记录列表已刷新（快捷词用户消息保存后）');
                } catch (error) {
                    console.error('快捷词用户消息保存后刷新历史记录列表失败:', error);
                }
            }

            // 现在调用API发送消息（不要覆盖messages数组）
            const response = await sendMessageToAPI(question, true); // 添加参数表示是快捷词
            console.log('AI回复完成');

            if (chatId) {

                // 获取所有非欢迎非思考中的系统消息
                const systemMessages = messages.value.filter(msg =>
                    msg.type === 'system' && // 是系统消息
                    !msg.welcome &&         // 不是欢迎消息
                    !msg.thinking           // 不是思考中状态
                );

                console.log('找到的系统消息数量:', systemMessages.length);

                // 找到最后一条系统消息
                if (systemMessages.length > 0) {
                    const lastSystemMessage = systemMessages[systemMessages.length - 1];

                    // 获取原始内容
                    let rawContent = '';

                    if (lastSystemMessage.originalContent) {
                        rawContent = lastSystemMessage.originalContent;
                    } else if (lastSystemMessage.content) {
                        rawContent = lastSystemMessage.content;
                    }

                    // 保存机器人回复
                    if (rawContent.trim()) {
                        console.log('准备调用addMessage保存机器人回复');

                        // 快捷词：保存原始内容，但将<think>标签转换为[THINKING]标签
                        let contentToSave = lastSystemMessage.originalContent || rawContent;

                        // 将所有思考标签转换为[THINKING]标签，用于历史记录保存
                        if (contentToSave.includes('<think>') || contentToSave.includes('<thinking>')) {
                            contentToSave = contentToSave
                                .replace(/<think>/g, '[THINKING]')
                                .replace(/<\/think>/g, '[/THINKING]')
                                .replace(/<thinking>/g, '[THINKING]')
                                .replace(/<\/thinking>/g, '[/THINKING]');
                            console.log('快捷词：已将思考标签转换为[THINKING]标签用于历史记录保存');
                        }

                        // 获取参考文档数据并拼接到内容后面
                        const docAggs = lastSystemMessage.docAggs || [];
                        if (docAggs.length > 0) {
                            const fileListContent = JSON.stringify(docAggs);
                            contentToSave += `[FILELIST]${fileListContent}[/FILELIST]`;
                            console.log('快捷词：已将参考文档拼接到内容后面:', docAggs);
                        }

                        console.log('快捷词：保存到历史记录的内容:', contentToSave);

                        // 处理内容转义
                        let finalContentToSave = contentToSave;
                        if (selectedMode.value.value === 'content_creation' && isNetworkEnabled.value) {
                            finalContentToSave = escapeHtmlContent(contentToSave);
                            console.log('内容创作联网模式：对快捷词机器人回复进行HTML转义处理');
                        }

                        console.log('思考部分输出完成，现在调用addMessage保存机器人回复');
                        await addMessage({
                            id: chatId, // 使用历史记录ID
                            conversationId: sessionId.value, // 使用会话ID
                            content: finalContentToSave, // 使用完整内容对象的JSON字符串
                            role: 'assistant',
                            repoId: currentRepoId // 添加 repoId 参数
                        });

                        // 机器人回复保存后延迟刷新历史记录列表
                        try {
                            console.log('快捷词：机器人回复保存后延迟刷新历史记录列表');
                            // 添加延迟确保数据库已更新
                            await new Promise(resolve => setTimeout(resolve, 300));
                            await fetchHistoryFromAPI();
                            console.log('历史记录列表已刷新（快捷词机器人回复保存后）');

                            // 机器人回复保存后，重新确保选中状态为第一个位置
                            console.log('快捷词：机器人回复保存后，准备重新设置选中状态，当前状态:', selectedHistoryIndex.value);
                            selectedHistoryIndex.value = 0;
                            console.log('快捷词：机器人回复保存后，已重新设置选中状态为第一个位置，当前状态:', selectedHistoryIndex.value);

                            // 强制触发DOM更新，确保选中状态正确显示
                            nextTick(() => {
                                console.log('快捷词：强制DOM更新，确保选中状态正确显示');
                            });
                        } catch (error) {
                            console.error('快捷词机器人回复保存后刷新历史记录列表失败:', error);
                        }
                    } else {
                        console.error('机器人回复内容为空，跳过保存');
                    }
                } else {
                    console.error('未找到系统回复消息');

                    // 最后一次尝试：获取最后一个消息
                    const lastIndex = messages.value.length - 1;
                    if (lastIndex >= 0 && messages.value[lastIndex].type === 'system' && !messages.value[lastIndex].thinking) {
                        const lastMsg = messages.value[lastIndex];
                        const rawContent = lastMsg.content || '';
                        if (rawContent) {
                            console.log('使用最后一条消息保存机器人回复');

                            // 快捷词（备用）：保存原始内容，但将<think>标签转换为[THINKING]标签
                            let contentToSave = lastMsg.originalContent || rawContent;

                            // 将所有思考标签转换为[THINKING]标签，用于历史记录保存
                            if (contentToSave.includes('<think>') || contentToSave.includes('<thinking>')) {
                                contentToSave = contentToSave
                                    .replace(/<think>/g, '[THINKING]')
                                    .replace(/<\/think>/g, '[/THINKING]')
                                    .replace(/<thinking>/g, '[THINKING]')
                                    .replace(/<\/thinking>/g, '[/THINKING]');
                                console.log('快捷词（备用）：已将思考标签转换为[THINKING]标签用于历史记录保存');
                            }

                            // 获取参考文档数据并拼接到内容后面
                            const docAggs = lastMsg.docAggs || [];
                            if (docAggs.length > 0) {
                                const fileListContent = JSON.stringify(docAggs);
                                contentToSave += `[FILELIST]${fileListContent}[/FILELIST]`;
                                console.log('快捷词（备用）：已将参考文档拼接到内容后面:', docAggs);
                            }

                            console.log('快捷词（备用）：保存到历史记录的内容:', contentToSave);

                            console.log('思考部分输出完成，现在调用addMessage保存机器人回复');
                            await addMessage({
                                id: chatId, // 使用历史记录ID
                                conversationId: sessionId.value, // 使用会话ID
                                content: contentToSave,
                                role: 'assistant',
                                repoId: currentRepoId // 添加 repoId 参数
                            });

                            // 机器人回复保存后延迟刷新历史记录列表（备用逻辑）
                            try {
                                console.log('快捷词（备用）：机器人回复保存后延迟刷新历史记录列表');
                                // 添加延迟确保数据库已更新
                                await new Promise(resolve => setTimeout(resolve, 300));
                                await fetchHistoryFromAPI();
                                console.log('历史记录列表已刷新（快捷词备用机器人回复保存后）');

                                // 机器人回复保存后，重新确保选中状态为第一个位置
                                console.log('快捷词（备用）：机器人回复保存后，准备重新设置选中状态，当前状态:', selectedHistoryIndex.value);
                                selectedHistoryIndex.value = 0;
                                console.log('快捷词（备用）：机器人回复保存后，已重新设置选中状态为第一个位置，当前状态:', selectedHistoryIndex.value);
                            } catch (error) {
                                console.error('快捷词备用机器人回复保存后刷新历史记录列表失败:', error);
                            }
                        }
                    }
                }
            } else {
                console.error('未获取到有效的chat_id，无法保存消息');
            }

            // 更新历史记录列表，但不更新当前显示的消息
            try {
                console.log('建议问题：更新历史记录开始（仅后台）');

                // 更新历史记录，但不改变当前显示的消息
                try {
                    const historyResponse = await getMessage();

                    if (historyResponse.code == 0 && historyResponse.data) {
                        console.log('历史记录API返回成功，但不更新当前消息列表');

                        // 保存当前选中的历史记录ID，用于更新后重新定位
                        let currentHistoryId = null;
                        if (selectedHistoryIndex.value !== -1) {
                            const allHistories = [
                                ...chatHistory.value.recent7Days,
                                ...chatHistory.value.recent30Days,
                                ...chatHistory.value.before30Days
                            ];
                            const currentHistory = allHistories.find(h => getHistoryFullIndex(h) === selectedHistoryIndex.value);
                            if (currentHistory) {
                                currentHistoryId = currentHistory.id;
                            }
                        }

                        // 更新chatHistory，但不影响当前messages
                        chatHistory.value = {
                            recent7Days: (historyResponse.data.recent7Days || []).map(enrichHistoryItem),
                            recent30Days: (historyResponse.data.recent30Days || []).map(enrichHistoryItem),
                            before30Days: (historyResponse.data.before30Days || []).map(enrichHistoryItem)
                        };

                        // 在快捷词模式下，总是确保选中第一个历史记录
                        selectedHistoryIndex.value = 0;
                        console.log('建议问题：确保选中第一个历史记录，索引设为0');
                    }
                } catch (fetchError) {
                    console.error('后台更新历史记录失败:', fetchError);
                }

                console.log('建议问题：历史记录已后台更新');
            } catch (error) {
                console.error('建议问题：更新历史记录失败:', error);
            }
        } catch (error) {
            console.error('保存对话历史失败，错误详情:', error);
        }
    } catch (error) {
        console.error('发送建议问题失败:', error);
        // 如果发送失败，移除最后一条思考中状态消息
        const lastIndex = messages.value.length - 1;
        if (lastIndex >= 0 && messages.value[lastIndex].thinking) {
            const errorMessage4 = '发送问题失败，请重试';
            messages.value[lastIndex] = {
                type: 'system',
                content: processContent(errorMessage4),
                typingCompleted: true,
                showReferences: false,
                canceled: true // 标记为已取消
            };
        }
    } finally {
        isProcessing.value = false;

        // 确保选中状态为第一个位置
        selectedHistoryIndex.value = 0;
        console.log('askSuggestedQuestion函数结束，确保选中状态为第一个位置:', selectedHistoryIndex.value);

        // 强制刷新DOM，确保选中状态正确显示
        nextTick(() => {
            console.log('askSuggestedQuestion结束后强制DOM更新，确保选中状态正确显示');

            // 再次确认选中状态
            selectedHistoryIndex.value = 0;
        });
    }
};

// 使用watchEffect监听消息变化，自动滚动
watchEffect(() => {
    // 监听消息数组的长度变化
    const messagesLength = messages.value.length;

    if (messagesLength > 0) {
        // 立即执行一次滚动
        nextTick(() => {
            // 只调用一次scrollToBottom，避免多次滚动导致布局问题
            scrollToBottom();

            // 添加一个延迟滚动，确保内容完全渲染后也能滚动到底部
            setTimeout(() => {
                if (chatContainer.value) {
                    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
                }
            }, 300);
        });
    }
});

// 监听打字完成状态，确保在打字效果完成后也会滚动
watch(typingCompleted, (newValue) => {
    if (newValue === true) {
        // 只在聊天容器内滚动，不影响整个页面
        if (chatContainer.value) {
            chatContainer.value.scrollTop = chatContainer.value.scrollHeight;

            // 添加一个延迟滚动，确保内容完全渲染后也能滚动到底部
            setTimeout(() => {
                if (chatContainer.value) {
                    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
                }
            }, 300);
        }
    }
});

// 监听移动设备状态变化
watch(isMobile, (newValue) => {
    if (newValue && messages.value.length > 0 && messages.value[0].welcome) {
        adaptSuggestedQuestions();
    } else if (!newValue && messages.value.length > 0 && messages.value[0].welcome) {
        // 恢复完整建议问题
        messages.value[0].suggestedQuestions = welcomeMessage.suggestedQuestions;
    }
});

// 下载文档
const downloadDocument = (documentId) => {
    if (!documentId) {
        showToast('无法下载：缺少文档ID', 'error');
        return;
    }

    try {
        // 构建下载URL
        const downloadUrl = `${getApiBaseUrl()}/document/${documentId}?ext=docx&prefix=document`;

        // 创建一个隐形链接进行下载
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.setAttribute('download', `document_${documentId}.docx`);
        link.setAttribute('target', '_blank');

        // 添加到DOM并触发点击
        document.body.appendChild(link);
        link.click();

        // 清理DOM
        document.body.removeChild(link);

        showToast('开始下载文档');
    } catch (e) {
        console.error('下载文档失败:', e);
        showToast('下载文档失败', 'error');
    }
};

// 获取唯一参考文献
const getUniqueReferences = (references) => {
    if (!references || !Array.isArray(references)) {
        return [];
    }

    const uniqueRefs = [];
    const seen = new Set();

    for (const ref of references) {
        if (ref && ref.document_id && !seen.has(ref.document_id)) {
            uniqueRefs.push(ref);
            seen.add(ref.document_id);
        }
    }

    return uniqueRefs;
};

// 去重参考文献
const deduplicateReferences = (references) => {
    return getUniqueReferences(references);
};

// 在 script setup 部分添加新的变量和方法
const activeController = ref(null);
let typingInterval = null; // 声明在全局范围内以便在cancelRequest中访问
let isTypingStarted = false; // 标记是否已开始打字效果
let displayedLength = 0; // 当前显示的文本长度

// 添加处理按钮点击的函数
const handleButtonClick = () => {

    if (isProcessing.value) {
        // 如果正在处理中，先强制设置处理状态为false以立即更新UI
        isProcessing.value = false;
        nextTick(() => {
            cancelRequest();
        });
    } else if (userMessage.value.trim()) {
        // 如果有输入内容且不在处理中，发送消息
        // console.log('准备调用sendMessage函数');
        sendMessage();
    }
};

// 取消正在进行的请求
const cancelRequest = () => {
    // console.log('开始取消请求');

    // 清除任何正在进行的定时器
    if (typingInterval) {
        // console.log('清除打字动画定时器');
        clearInterval(typingInterval);
        typingInterval = null;
        // 重置打字状态
        isTypingStarted = false;
        displayedLength = 0;
    }

    if (activeController.value) {
        // console.log('中止请求控制器');
        try {
            // 中止请求
            activeController.value.abort();

            // 更新最后一条消息，显示已停止发送
            const lastIndex = messages.value.length - 1;
            if (messages.value[lastIndex]) {
                console.log('更新消息为已停止发送状态');
                const errorMessage5 = '已停止发送';
                messages.value[lastIndex] = {
                    type: 'system',
                    content: processContent(errorMessage5),
                    typingCompleted: true,
                    showReferences: false,
                    canceled: true // 标记为已取消
                };

                // 强制组件刷新
                nextTick(() => {
                    console.log('消息更新后的状态:', messages.value[lastIndex]);
                    // 确认最新消息的状态
                    const latestMessage = messages.value[messages.value.length - 1];
                    console.log('最新消息是否标记为已取消:', latestMessage.canceled);
                });
            } else {
                console.log('没有找到思考中的消息:', messages.value);
            }

            showToast('已停止发送', 'info');
        } catch (error) {
            console.error('取消请求时出错:', error);
        } finally {
            // 确保控制器引用被清除
            activeController.value = null;
        }
    } else {
        console.log('没有活动的请求控制器');
    }
};

// 添加这个手表监听器在 script setup 区域
watch(selectedMode, async (newMode, oldMode) => {
    // 只有当值真正变化时才处理
    if (oldMode && newMode.value !== oldMode.value) {
        // 显示切换模式的提示
        showToast(`已切换到${newMode.text}模式`, 'info');

        // 检查是否是从历史记录加载导致的模式变化
        console.log('模式变化检测，标志状态:',
            window.isSettingModeFromHistory ? '来自历史记录' : '手动切换');

        // 如果是从历史记录加载导致的模式变化，不创建新会话
        if (window.isSettingModeFromHistory) {
            console.log('从历史记录加载自动设置模式，不创建新会话');
            // 重置标志，避免影响后续操作
            window.isSettingModeFromHistory = false;
        }
        // 注意：手动切换模式的逻辑已经移到 handleModeChange 函数中
        // 这里不再重复执行，避免创建两次新会话
    }
}, { deep: true });

// 添加计算属性，根据日期对历史记录进行分组
const recentHistory = computed(() => {
    // 获取7天前的时间戳
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    // 如果历史记录为空且未触发过加载，触发一次加载
    if (chatHistory.value.length === 0 && !historyLoadAttempted.value) {
        historyLoadAttempted.value = true;
        fetchHistoryFromAPI().catch(error => {
            console.error('自动加载历史记录失败:', error);
        });
    }

    return chatHistory.value.filter(history => {
        const historyDate = parseHistoryDate(history.timestamp);
        return historyDate >= sevenDaysAgo;
    });
});

const olderHistory = computed(() => {
    // 获取7天和30天前的时间戳
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    return chatHistory.value.filter(history => {
        const historyDate = parseHistoryDate(history.timestamp);
        return historyDate < sevenDaysAgo && historyDate >= thirtyDaysAgo;
    });
});

// 获取历史记录在完整列表中的索引
const getHistoryFullIndex = (history) => {
    // 在三个分组中查找历史记录
    const recent7Index = chatHistory.value.recent7Days.findIndex(item => item.id === history.id);
    if (recent7Index !== -1) {
        // 如果是第一个元素，返回0，确保快捷词模式下能正确高亮
        if (recent7Index === 0 && selectedHistoryIndex.value === 0) {
            return 0;
        }
        return `recent7Days_${recent7Index}`;
    }

    const recent30Index = chatHistory.value.recent30Days.findIndex(item => item.id === history.id);
    if (recent30Index !== -1) {
        return `recent30Days_${recent30Index}`;
    }

    const before30Index = chatHistory.value.before30Days.findIndex(item => item.id === history.id);
    if (before30Index !== -1) {
        return `before30Days_${before30Index}`;
    }

    return -1;
};

// 解析历史记录的日期字符串
const parseHistoryDate = (dateStr) => {
    if (!dateStr) return new Date();

    // 假设格式为 "YYYY-MM-DD HH:MM:SS"
    try {
        const [datePart, timePart] = dateStr.split(' ');
        const [year, month, day] = datePart.split('-').map(Number);
        const [hours, minutes, seconds] = timePart ? timePart.split(':').map(Number) : [0, 0, 0];

        return new Date(year, month - 1, day, hours, minutes, seconds);
    } catch (e) {
        console.error('日期解析错误:', e);
        return new Date(); // 返回当前日期作为回退
    }
};

// 格式化历史记录日期显示
const formatHistoryDate = (dateStr) => {
    if (!dateStr) return '';

    // 处理API返回的日期格式或本地格式
    const historyDate = parseHistoryDate(dateStr);

    // 直接返回完整的日期时间格式
    const year = historyDate.getFullYear();
    const month = String(historyDate.getMonth() + 1).padStart(2, '0');
    const day = String(historyDate.getDate()).padStart(2, '0');
    const hours = historyDate.getHours().toString().padStart(2, '0');
    const minutes = historyDate.getMinutes().toString().padStart(2, '0');
    const seconds = historyDate.getSeconds().toString().padStart(2, '0');

    // 返回完整的日期时间格式：YYYY-MM-DD HH:MM:SS
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 添加模拟数据方法
const addMockHistoryData = () => {
    // 不再添加模拟数据
    return;
};

// 日期格式化工具函数
const formatDateForHistory = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 处理手动模式切换
const handleModeChange = async () => {
    // 设置标志，表明这是手动切换模式
    window.isSettingModeFromHistory = false;
    console.log('手动切换模式，设置标志为:', window.isSettingModeFromHistory);

    // 手动切换模式时，直接执行创建新会话的逻辑
    console.log('手动切换模式，主动开启新对话');

    // 1. 重置会话ID
    sessionId.value = '';
    localStorage.removeItem('current_conversation_id');
    console.log('模式切换：已清除会话ID');

    // 2. 重置历史记录索引
    selectedHistoryIndex.value = -1;

    // 3. 清空当前对话内容
    messages.value = [];
    console.log('已清空当前对话内容');

    // 重置参考文献和参考文档数据
    latestReferenceChunks.value = [];
    latestDocAggs.value = [];
    console.log('已重置参考文献和参考文档数据');

    // 4. 创建新会话
    console.log('手动切换模式，立即创建新会话');
    isProcessing.value = true;
    const result = await createSession();
    isProcessing.value = false;

    if (result) {
        console.log('模式切换：新会话创建成功，会话ID:', sessionId.value);

        // 确保在任何模式下都显示建议问题
        if (messages.value.length > 0 && messages.value[0].welcome) {
            messages.value[0].suggestedQuestions = welcomeMessage.suggestedQuestions;
        }

        // 滚动到底部显示新消息
        nextTick(() => scrollToBottom());

        console.log('模式切换：完成会话创建，等待用户输入第一条消息');
    }
};

// 功能卡片点击切换模式
const switchToMode = async (modeValue) => {
    console.log('功能卡片点击，切换到模式:', modeValue);

    // 找到对应的模式选项
    const targetMode = modeOptions.find(mode => mode.value === modeValue);
    if (!targetMode) {
        console.error('未找到对应的模式选项:', modeValue);
        return;
    }

    // 设置新模式
    selectedMode.value = targetMode;
    console.log('已设置新模式:', selectedMode.value);

    // 调用模式切换处理函数
    await handleModeChange();
};

// 切换联网状态
const toggleNetwork = () => {
    isNetworkEnabled.value = !isNetworkEnabled.value;
    console.log('联网状态切换为:', isNetworkEnabled.value ? '联网' : '不联网');
    showToast(`已${isNetworkEnabled.value ? '开启' : '关闭'}联网`, 'info');
};

// 根据模式获取conversationType
const getConversationType = (modeValue) => {
    switch (modeValue) {
        case 'v2':
            return 'DATA_QUERY';
        case 'content_creation':
            return 'CONTENT_CREATION';
        case 'policy_qa':
            return 'POLICY_QA';
        case 'WORK_REPORT':
            return 'WORK_REPORT';
        case 'r1':
        default:
            return 'DAILY_QA';
    }
};

// 获取输入提示文本
const getInputDisclaimer = () => {
    switch (selectedMode.value.value) {
        case 'v2':
            return '提示: 您可以询问实地问题分析';
        case 'content_creation':
            return '提示: 您可以进行文案生成，公文辅助写作，语言翻译等内容创作';
        case 'policy_qa':
            return '提示: 您可以询问政策问答，办事指引，法规条例等相关问题';
        case 'WORK_REPORT':
            return '提示: 您可以进行创城工作总结，报告生成，进度分析等相关工作';
        case 'r1':
        default:
            return '提示: 您可以根据知识库内容进行提问';
    }
};

// 检查文件列表数据
const checkFileListData = () => {
    console.log('当前文件列表数据:', fileList.value);
    if (fileList.value.length > 0) {
        console.log('第一个文件的完整数据:', fileList.value[0]);
        console.log('permissionType字段存在:', 'permissionType' in fileList.value[0]);
        console.log('permissionType值:', fileList.value[0].permissionType);

        // 添加更多调试信息
        console.log('当前用户ID:', userStore.id);
        console.log('当前用户名:', userStore.name);
        console.log('文件创建者:', fileList.value[0].createBy);
        console.log('是否为创建者:', fileList.value[0].createBy === userStore.name);
        console.log('文件状态:', fileList.value[0].status);
        console.log('activeMenuGroup:', activeMenuGroup.value);

        // 检查按钮显示条件
        console.log('取消共享按钮显示条件:',
            fileList.value[0].createBy === userStore.name &&
            (fileList.value[0].permissionType === 'UNIT' || fileList.value[0].permissionType === 'ALL'));

        console.log('分享按钮显示条件:',
            fileList.value[0].createBy === userStore.name &&
            fileList.value[0].permissionType !== 'UNIT' &&
            fileList.value[0].permissionType !== 'ALL' &&
            activeMenuGroup.value !== 'unit');
    } else {
        console.log('文件列表为空');
    }
};

// 在onMounted钩子中添加获取历史记录的API调用
onMounted(async () => {
    // 初始化全局标志，用于区分模式变化是从历史记录加载还是手动切换
    window.isSettingModeFromHistory = false;
    window.isManualModeChange = false; // 新增标志，表示是否是手动切换模式
    console.log('初始化isSettingModeFromHistory标志为:', window.isSettingModeFromHistory);

    console.log('getApiBaseUrl-------------------------------->', baseURL);
    // console.log('axios.defaults.baseURLaxios.defaults.baseURL', axios.defaults.baseURL)
    // 初始设置 preventInitialScroll 为 true，防止页面加载时自动滚动
    preventInitialScroll.value = true;

    // 设置一个定时器，在页面加载 3 秒后允许滚动操作
    setTimeout(() => {
        preventInitialScroll.value = false;
        // console.log('页面初始化完成，允许滚动操作');
    }, 3000);

    // 检查并获取publicKey
    const publicKey = await checkAndGetPublicKey();
    // console.log('获取的publicKey:', publicKey ? '成功' : '失败');

    // 解密并打印API凭证
    const decryptResult = await decryptApiCredentials();
    // console.log('API凭证解密:', decryptResult ? '成功' : '失败');

    // 输出当前登录用户信息
    printCurrentUserInfo();

    // 注册Element Plus的Infinite Scroll指令
    document.querySelector('.file-list')?.setAttribute('v-infinite-scroll', '');

    // 获取左侧知识库列表
    await getLeftList();

    // 尝试恢复之前保存的知识库选择状态
    const restored = await restoreKnowledgeBaseState();

    // 如果没有恢复成功，则设置默认状态
    if (!restored) {
        // 设置初始菜单状态为单位知识库(全部)
        activeMenuGroup.value = 'unit';
        activeMenuItem.value = 'all';

        // 同步更新问答区域知识库选择为单位知识库
        selectedKnowledgeBase.value = {
            text: '单位知识库（全部）',
            value: 'unit'
        };

        // 页面加载后默认加载单位知识库(全部)的文件列表
        // console.log('开始加载文件列表...');
        await loadKnowledgeBaseFiles(null);
        // console.log('文件列表加载完成，数量:', fileList.value.length);
    }

    // 延时检查，确保文件列表正确加载
    setTimeout(() => {
        if (fileList.value.length === 0) {
            // console.log('首次加载未成功，尝试重新加载文件列表');
            loadKnowledgeBaseFiles(null);
        }
    }, 2000);

    // 检查文件列表数据
    setTimeout(checkFileListData, 2000); // 延迟2秒执行，确保数据已加载
    setTimeout(checkFileListData, 5000); // 再次检查，确保所有数据都加载完成

    // 从API获取历史记录，不再使用localStorage
    await fetchHistoryFromAPI();

    // 初始检测设备类型和方向
    checkDeviceAndOrientation();

    // 添加窗口大小变化监听
    window.addEventListener('resize', checkDeviceAndOrientation);

    // 添加滚动事件监听，控制返回顶部按钮显示
    window.addEventListener('scroll', handleScroll);

    // 添加思考完成事件监听器
    document.addEventListener('thinking-completed', () => {
        console.log('收到思考完成事件，确保思考步骤可见');
        ensureThinkingStepsVisible();
    });

    // 添加DOM变化监听，确保思考步骤始终可见
    document.addEventListener('DOMContentLoaded', ensureThinkingStepsVisible);
    document.addEventListener('DOMNodeInserted', ensureThinkingStepsVisible);

    // 设置定时器，周期性地确保思考步骤可见
    // const visibilityInterval = setInterval(() => {
    //   console.log('定时检查并确保思考步骤可见');
    //   ensureThinkingStepsVisible();
    // }, 3000);

    // 保存定时器ID，以便在组件卸载时清除
    // window.thinkingVisibilityInterval = visibilityInterval;

    // 页面初始化时自动创建新会话
    console.log('页面初始化完成，开始创建初始会话');
    try {
        // 确保不在查看历史记录状态
        selectedHistoryIndex.value = -1;
        // 调用创建新会话接口
        await createNewSession(false);
        console.log('初始会话创建成功');
    } catch (error) {
        console.error('初始会话创建失败:', error);
    }

    // 移除页面初始化时的自动滚动
    // scrollToBottom();

    // 在组件挂载后初始化highlight.js
    nextTick(() => {
        // 初始化highlight.js，确保能够正确高亮显示代码块
        document.querySelectorAll('pre code').forEach((block) => {
            hljs.highlightElement(block);
        });
    });

    // 监听消息变化，当有新消息时重新应用代码高亮
    watch(() => messages.value, () => {
        nextTick(() => {
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightElement(block);
            });
        });
    }, { deep: true });

    // 添加页面离开警告和上传状态保存功能
    window.addEventListener('beforeunload', handleBeforeUnload);

    // 检查是否有未完成的上传
    checkPendingUploads();

    // 检查uploadStore中是否有正在上传的文件
    if (uploadStore.uploadingFiles.length > 0) {
        console.log('检测到uploadStore中有正在上传的文件:', uploadStore.uploadingFiles.length);

        // 将uploadStore中的文件同步到本地fileList
        uploadStore.uploadingFiles.forEach(storeFile => {
            // 检查文件是否已经在列表中
            const existingIndex = fileList.value.findIndex(file => file.id === storeFile.id);
            if (existingIndex === -1) {
                // 添加到文件列表顶部
                fileList.value.unshift({
                    ...storeFile,
                    // 确保显示正确的状态和进度
                    status: storeFile.status,
                    progress: storeFile.progress
                });
            } else {
                // 更新现有文件的状态和进度
                fileList.value[existingIndex].status = storeFile.status;
                fileList.value[existingIndex].progress = storeFile.progress;
            }
        });

        // 设置上传状态
        isUploading.value = true;
    }

    // 组件卸载时移除事件监听和定时器
    onUnmounted(() => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
        document.removeEventListener('thinking-completed', ensureThinkingStepsVisible);
        document.removeEventListener('DOMContentLoaded', ensureThinkingStepsVisible);
        document.removeEventListener('DOMNodeInserted', ensureThinkingStepsVisible);

        // 清除思考步骤可见性检查定时器
        if (window.thinkingVisibilityInterval) {
            clearInterval(window.thinkingVisibilityInterval);
            window.thinkingVisibilityInterval = null;
        }
    });
});

// 页面离开处理函数 - 已移除警告提示
const handleBeforeUnload = (e) => {
    // 只保存上传状态到localStorage，不再显示警告
    saveUploadState();

    // 不再阻止用户离开页面
    // 注释：已移除"文件正在上传中，离开页面将中断上传"的警告
};

// 保存上传状态到localStorage
const saveUploadState = () => {
    // 找出所有正在上传的文件
    const uploadingFiles = fileList.value.filter(file =>
        file.status === 'UPLOADING' || file.status === 'PARSING'
    );

    if (uploadingFiles.length > 0) {
        try {
            // 只保存必要的信息，不保存文件内容
            const uploadState = uploadingFiles.map(file => ({
                id: file.id,
                fileName: file.fileName,
                status: file.status,
                progress: file.progress,
                fileId: file.fileId,
                filePath: file.filePath,
                timestamp: new Date().getTime()
            }));

            localStorage.setItem('pendingUploads', JSON.stringify(uploadState));
            // console.log('已保存上传状态:', uploadState);
        } catch (error) {
            // console.error('保存上传状态失败:', error);
        }
    }
};

// 检查是否有未完成的上传 - 已移除提示
const checkPendingUploads = () => {
    try {
        // 直接清除localStorage中的未完成上传记录，不再显示提示
        localStorage.removeItem('pendingUploads');
        console.log('已清除未完成上传记录，不再显示提示');
    } catch (error) {
        console.error('清除未完成上传记录失败:', error);
    }
};

onUnmounted(() => {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', checkDeviceAndOrientation);

    // 清除所有定时器
    if (toast.value.timeout) {
        clearTimeout(toast.value.timeout);
    }

    // 清除所有上传相关的定时器
    fileList.value.forEach(file => {
        if (file.id) {
            // 清除进度条相关定时器
            clearInterval(window['progressInterval_' + file.id]);
            clearInterval(window['dotsAnimation_' + file.id]);
            clearInterval(window['autoProgress_' + file.id]);
            console.log('卸载组件时清除文件', file.id, '的所有定时器');
        }
    });
});

// 添加菜单弹窗
const historyMenuOpen = ref(false);
const selectedHistoryItem = ref(null);
const historyMenuActivator = ref(null);
const newHistoryTitle = ref('');
const historyToRename = ref(null);

// 打开重命名对话框
const renameHistory = (history) => {
    if (!history) return;

    // 设置要重命名的历史记录和新标题
    historyToRename.value = history;
    newHistoryTitle.value = history.title || '';

    // 打开重命名对话框
    showRenameDialog.value = true;

    // 关闭菜单
    closeHistoryMenu();

    // 在下一个tick后聚焦输入框
    nextTick(() => {
        const input = document.querySelector('.rename-dialog-input');
        if (input) {
            input.focus();
        }
    });
};

// 确认重命名
const confirmRename = async () => {
    if (!historyToRename.value || !newHistoryTitle.value.trim()) return;

    try {
        const history = historyToRename.value;
        const newTitle = newHistoryTitle.value.trim();

        // 如果有API数据，通过API更新标题
        // 注意：这里假设API有更新标题的功能，实际需要根据API情况调整
        if (history.apiData && history.apiData.id) {
            console.log('准备通过API更新历史记录标题:', history.apiData.id, newTitle);

            // 这里可以添加调用API更新标题的代码
            // 例如:
            // const response = await updateHistoryTitle({
            //   id: history.apiData.id,
            //   title: newTitle
            // });

            // 由于当前API可能没有直接修改标题的功能，这里先在本地更新
            // console.log('API更新标题功能暂未实现，仅在本地更新');
        }

        // 更新本地标题
        const index = chatHistory.value.findIndex(h => h.id === history.id);
        if (index !== -1) {
            chatHistory.value[index].title = newTitle;

            // 如果有API数据，也更新API数据中的标题
            if (chatHistory.value[index].apiData) {
                chatHistory.value[index].apiData.conversationTitle = newTitle;
            }

            // 如果重命名的是当前选中的对话，也更新选中状态
            if (selectedHistoryIndex.value === index) {
                selectedHistoryItem.value = chatHistory.value[index];
            }
        }

        // 关闭对话框
        showRenameDialog.value = false;
        historyToRename.value = null;
        newHistoryTitle.value = '';

        // 显示成功提示
        showToast('对话已重命名');

        // 刷新历史记录列表
        await fetchHistoryFromAPI();
    } catch (e) {
        console.error('重命名失败:', e);
        showToast('重命名失败，请重试', 'error');
    }
};

// 删除历史记录
const deleteHistory = (history) => {
    historyToDelete.value = history;
    showDeleteConfirmDialog.value = true;
};

// 添加确认删除的方法
const confirmDelete = async () => {
    if (!historyToDelete.value) return;

    try {
        const history = historyToDelete.value;

        // 检查删除的是否是当前正在查看的历史记录
        const isCurrentlyViewing = selectedHistoryIndex.value !== -1;
        let isViewingThisHistory = false;

        if (isCurrentlyViewing) {
            // 通过getHistoryFullIndex来比较，因为selectedHistoryIndex存储的是字符串格式的索引
            const deletingHistoryIndex = getHistoryFullIndex(history);
            isViewingThisHistory = selectedHistoryIndex.value === deletingHistoryIndex;

            // console.log('删除历史记录 - 当前选中索引:', selectedHistoryIndex.value);
            // console.log('删除历史记录 - 要删除的记录索引:', deletingHistoryIndex);
            // console.log('删除历史记录 - 索引是否匹配:', isViewingThisHistory);
        }

        // console.log('删除历史记录 - 当前是否在查看历史记录:', isCurrentlyViewing);
        // console.log('删除历史记录 - 删除的是否是当前查看的记录:', isViewingThisHistory);
        // console.log('删除历史记录 - 要删除的记录ID:', history.apiData?.id);
        // console.log('删除历史记录 - 要删除的记录对象:', history);

        // 先关闭对话框，确保无论后续操作是否成功，对话框都会关闭
        showDeleteConfirmDialog.value = false;

        // 如果有API数据，使用API删除
        if (history.apiData && history.apiData.id) {
            console.log('准备通过API删除历史记录:', history.apiData.id);

            // 调用删除API，使用ids参数传递id值
            const response = await removeMessage({
                ids: history.apiData.id
            });

            console.log('删除历史记录API响应:', response);

            if (response.code == 0) {
                console.log('删除成功');

                // 如果删除的是当前正在查看的历史记录，需要清空当前内容并开启新对话
                if (isViewingThisHistory) {
                    console.log('删除的是当前正在查看的历史记录，重新清空当前内容并开启新对话');
                    try {
                        // 调用创建新会话方法，这会清空当前内容并创建新会话
                        await createNewSession(false);
                        console.log('已成功清空当前内容并开启新对话');
                    } catch (error) {
                        console.error('开启新对话失败:', error);
                        // 即使开启新对话失败，也要确保清空当前内容
                        messages.value = [];
                        latestReferenceChunks.value = [];
                        latestDocAggs.value = [];
                        selectedHistoryIndex.value = -1;
                        console.log('已手动清空当前内容');
                    }
                }

                // 删除成功后立即调用列表接口刷新数据
                try {
                    console.log('删除成功，立即刷新历史记录列表');

                    // 如果删除的不是当前查看的历史记录，需要保存当前查看的历史记录信息以便恢复选中状态
                    let currentViewingHistoryId = null;
                    if (isCurrentlyViewing && !isViewingThisHistory) {
                        // 获取当前查看的历史记录ID
                        const allHistories = [
                            ...chatHistory.value.recent7Days,
                            ...chatHistory.value.recent30Days,
                            ...chatHistory.value.before30Days
                        ];
                        const currentViewingHistory = allHistories.find(h => getHistoryFullIndex(h) === selectedHistoryIndex.value);
                        if (currentViewingHistory && currentViewingHistory.apiData) {
                            currentViewingHistoryId = currentViewingHistory.apiData.id;
                            console.log('保存当前查看的历史记录ID以便恢复选中状态:', currentViewingHistoryId);
                        }
                    }

                    await getMessage().then(listRes => {
                        if (listRes.code == 0 && listRes.data) {
                            // 更新历史记录，保持分组结构
                            chatHistory.value = {
                                recent7Days: (listRes.data.recent7Days || []).map(enrichHistoryItem),
                                recent30Days: (listRes.data.recent30Days || []).map(enrichHistoryItem),
                                before30Days: (listRes.data.before30Days || []).map(enrichHistoryItem)
                            };

                            // 如果删除的是当前查看的历史记录，重置选中状态
                            if (isViewingThisHistory) {
                                selectedHistoryIndex.value = -1;
                                console.log('删除的是当前查看的记录，已重置选中状态');
                            }
                            // 如果删除的不是当前查看的历史记录，尝试恢复选中状态
                            else if (currentViewingHistoryId) {
                                const allUpdatedHistories = [
                                    ...chatHistory.value.recent7Days,
                                    ...chatHistory.value.recent30Days,
                                    ...chatHistory.value.before30Days
                                ];
                                const restoredHistory = allUpdatedHistories.find(h => h.apiData && h.apiData.id === currentViewingHistoryId);
                                if (restoredHistory) {
                                    const newIndex = getHistoryFullIndex(restoredHistory);
                                    selectedHistoryIndex.value = newIndex;
                                    console.log('已恢复当前查看历史记录的选中状态，新索引:', newIndex);
                                } else {
                                    selectedHistoryIndex.value = -1;
                                    console.log('未找到当前查看的历史记录，重置选中状态');
                                }
                            } else {
                                selectedHistoryIndex.value = -1;
                                console.log('无需恢复选中状态，重置为-1');
                            }

                            console.log('历史记录列表已成功刷新');
                        } else {
                            console.error('获取历史记录列表失败:', listRes?.msg || '未知错误');
                        }
                    });
                } catch (listError) {
                    console.error('刷新历史记录列表失败:', listError);
                    // 即使刷新失败也不影响删除操作的完成
                }

                // 显示提示
                showToast('历史记录已删除');
            } else {
                console.error('删除失败:', response);
                // throw new Error('删除失败');
            }
        }

        // 清除删除的历史记录引用
        historyToDelete.value = null;

    } catch (e) {
        // 即使发生错误，也确保对话框关闭
        showDeleteConfirmDialog.value = false;
        historyToDelete.value = null;

        console.error('删除历史记录失败:', e);
        showToast('删除失败', 'error');
    }
};

// 添加菜单位置状态
const menuPosition = ref({ x: 0, y: 0 });

// 修改打开历史记录菜单的方法
const openHistoryMenu = (event, history) => {
    event.stopPropagation();
    // 设置选中的历史记录项
    selectedHistoryItem.value = history;
};

// 处理点击外部关闭菜单
const handleClickOutside = (event) => {
    const menu = document.querySelector('.v-menu__content');
    const button = event.target.closest('.history-menu-btn');

    // 如果点击的是按钮本身，不做处理（让openHistoryMenu处理）
    if (button) return;

    // 如果点击的不是菜单内部元素，则关闭菜单
    if (menu && !menu.contains(event.target)) {
        closeHistoryMenu();
    }
};

// 关闭历史记录菜单
const closeHistoryMenu = () => {
    historyMenuOpen.value = false;
    selectedHistoryItem.value = null;
    document.removeEventListener('click', handleClickOutside);
};

// 添加关闭菜单的监听
watch(historyMenuOpen, (newValue) => {
    if (!newValue) {
        // 菜单关闭时重置状态
        selectedHistoryItem.value = null;
        menuPosition.value = { x: 0, y: 0 };
    }
});

// 重命名对话框
const showRenameDialog = ref(false);

// 添加新的变量来控制历史记录组的展开状态
const groupExpanded = ref({
    recent: true,
    older: true,
    oldest: true
});

// 添加新的方法来切换历史记录组的展开状态
const toggleGroup = (group) => {
    groupExpanded.value[group] = !groupExpanded.value[group];
};
const isJoin = ref(false);
// 添加侧边栏折叠状态
const isSidebarCollapsed = ref(false);

// 切换侧边栏状态
const toggleSidebar = () => {
    isSidebarCollapsed.value = !isSidebarCollapsed.value;
};

// 在 script setup 部分添加新的变量和方法
const showDeleteConfirmDialog = ref(false);
const historyToDelete = ref(null);

// 在script setup部分添加处理方法
// 添加下拉菜单命令处理方法
const handleCommand = (command) => {
    const { type, history } = command;
    if (type === 'rename') {
        renameHistory(history);
    } else if (type === 'delete') {
        deleteHistory(history);
    }
};

// 滚动到聊天容器
const scrollToChatContainer = () => {
    // 使用更长的延时确保DOM完全渲染
    setTimeout(() => {
        const chatSection = document.getElementById('chatSection');
        if (chatSection) {
            // 使用原生scrollIntoView方法
            chatSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // 等待滚动基本完成后再调整位置
            setTimeout(() => {
                // 向下滚动50px，确保元素在视口顶部下方50px处
                window.scroll(0, 100);

                // 添加高亮效果，吸引用户注意
                chatSection.classList.add('highlight-section');

                // 3秒后移除高亮效果
                setTimeout(() => {
                    chatSection.classList.remove('highlight-section');
                }, 3000);
            }, 800); // 更长的等待时间，确保第一次滚动完成
        }
    }, 300); // 更长的延时等待DOM渲染
};

// 滚动到聊天容器顶部
const scrollToTop = () => {
    if (chatContainer.value) {
        chatContainer.value.scrollTop = 0;
    }
    // 同时滚动整个页面到顶部
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
};

// 在 script setup 部分添加新的变量和方法
const menuExpanded = ref({
    public: false,
    unit: true
});

const activeMenuGroup = ref('unit');
const activeMenuItem = ref('create');

// 添加函数保存当前知识库选择状态到localStorage
const saveKnowledgeBaseState = () => {
    try {
        const state = {
            activeMenuGroup: activeMenuGroup.value,
            activeMenuItem: activeMenuItem.value,
            repoIds: repoIds.value
        };
        localStorage.setItem('kb_active_state', JSON.stringify(state));
        console.log('已保存知识库选择状态:', state);
    } catch (error) {
        console.error('保存知识库选择状态失败:', error);
    }
};

// 添加函数从localStorage恢复知识库选择状态
const restoreKnowledgeBaseState = async () => {
    try {
        const savedState = localStorage.getItem('kb_active_state');
        if (savedState) {
            const state = JSON.parse(savedState);
            console.log('恢复知识库选择状态:', state);

            // 恢复状态
            activeMenuGroup.value = state.activeMenuGroup || 'unit';
            activeMenuItem.value = state.activeMenuItem || 'all';
            repoIds.value = state.repoIds ? (typeof state.repoIds === 'string' ? parseInt(state.repoIds) : state.repoIds) : '';

            // 如果有选择特定知识库，加载对应文件
            if (state.repoIds) {
                await loadKnowledgeBaseFiles(state.repoIds);
            } else {
                await loadKnowledgeBaseFiles(null);
            }

            return true;
        }
    } catch (error) {
        console.error('恢复知识库选择状态失败:', error);
    }
    return false;
};

const toggleMenu = (group) => {
    menuExpanded.value[group] = !menuExpanded.value[group];
};

const selectMenuItem = async (group, item, repo) => {
    console.log('点击了吗？', group, item, repo);

    // 设置标志，阻止自动滚动
    isSwitchingKnowledgeBase.value = true;

    // 切换知识库时停止文件状态轮询
    stopFileStatusPolling();

    // 打印知识库完整数据
    if (repo != null && repo != undefined && repo != '') {
        console.log('选择的知识库完整数据:', repo);
        console.log('知识库详细属性:', {
            repoId: repo.repoId,
            repoDesc: repo.repoDesc,
            remark: repo.remark,
            isJoined: repo.isJoined,
            operationPermission: repo.operationPermission,
            createTime: repo.createTime,
            updateTime: repo.updateTime,
            createBy: repo.createBy,
            updateBy: repo.updateBy
        });
        isJoin.value = repo.isJoined
        filePermissionType.value = 'REPO';
    } else {
        console.log('未选择特定知识库或知识库数据为空');
        isJoin.value = false
        filePermissionType.value = 'ALL';
    }

    // 注释：已移除文件上传中切换知识库的限制，允许用户在文件上传过程中自由切换知识库

    // 确保uploadStore状态正确，不影响其他功能
    if (uploadStore.isUploading) {
        // 不再阻止切换，但仍然记录日志以便调试
        console.log('有文件正在上传中，但允许切换知识库');
    }

    // 切换到新的菜单组，并清除之前的活动状态
    activeMenuGroup.value = group;
    activeMenuItem.value = item;

    // 保存选择状态到localStorage
    saveKnowledgeBaseState();

    // 如果菜单是折叠的，展开它
    if (!menuExpanded.value[group]) {
        menuExpanded.value[group] = true;
    }

    // 如果在移动设备上，选择后自动收起侧边栏
    if (isMobile.value) {
        isLeftSidebarOpen.value = false;
    }

    // 处理点击公共知识库广场的情况
    if (group === 'public' && item === 'square') {
        console.log('点击公共知识库广场');
        // 设置知识库ID为null，因为公共知识库广场不需要特定ID
        repoIds.value = null;

        // 同步更新问答区域知识库选择为单位知识库
        selectedKnowledgeBase.value = {
            text: '单位知识库（全部）',
            value: 'unit'
        };

        // 调用接口加载公共知识库广场文件列表
        await loadKnowledgeBaseFiles(null);

        // 切换知识库后重新创建会话
        await createNewChatSession(null);

        // 在函数结束时重置标志
        setTimeout(() => {
            isSwitchingKnowledgeBase.value = false;
            console.log('公共知识库广场切换完成，重置滚动阻止标志');
        }, 1000);

        return;
    }

    // 处理点击单位知识库(全部)的情况
    if (group === 'unit' && item === 'all') {
        // 单位知识库使用null作为ID
        repoIds.value = null;
        // 同步更新问答区域知识库选择为单位知识库
        selectedKnowledgeBase.value = {
            text: '单位知识库（全部）',
            value: 'unit'
        };
        await loadKnowledgeBaseFiles(null);

        // 切换知识库后重新创建会话
        await createNewChatSession(null);

        // 在函数结束时重置标志
        setTimeout(() => {
            isSwitchingKnowledgeBase.value = false;
            console.log('单位知识库(全部)切换完成，重置滚动阻止标志');
        }, 1000);

        return;
    }

    // 如果选择了知识库，调用getFileList接口查询数据
    if (item && item.startsWith('repo_')) {
        const repoId = parseInt(item.split('_')[1]);
        repoIds.value = repoId;

        // 获取知识库信息并更新问答区域选择器
        const repo = repoList.value.find(r => r.repoId == repoId);
        if (repo) {
            // 输出知识库的remark字段
            console.log('知识库remark字段:', repo.remark);

            // 如果有remark字段，显示提示信息并说明它就是上传文件时使用的dataset_id
            if (repo.remark) {
                // ElMessage({
                //   message: `知识库备注(dataset_id): ${repo.remark}`,
                //   type: 'info',
                //   duration: 3000
                // });
            }

            // 同步更新问答区域知识库选择为当前选中的知识库
            selectedKnowledgeBase.value = {
                text: repo.repoDesc,
                value: 'repo_' + repoId
            };
        }

        if (repoId) {
            await loadKnowledgeBaseFiles(repoId);

            // 切换知识库后重新创建会话
            await createNewChatSession(repoId);
        }
    }

    // 在函数结束时重置标志
    // 使用setTimeout确保所有异步操作完成后再重置
    setTimeout(() => {
        isSwitchingKnowledgeBase.value = false;
        console.log('知识库切换完成，重置滚动阻止标志');
    }, 1000);
};

// 添加新函数用于处理知识库切换
const createNewChatSession = async (repoId) => {
    console.log('知识库切换，处理知识库变更，知识库ID:', repoId);

    // 检查是否正在查看历史记录
    const wasViewingHistory = selectedHistoryIndex.value !== -1;
    if (wasViewingHistory) {
        console.log('正在查看历史记录，切换知识库时重新创建新对话');
        // 重置历史记录选中状态
        selectedHistoryIndex.value = -1;
        // 清除localStorage中保存的会话ID
        localStorage.removeItem('current_conversation_id');
    } else {
        // 保存当前对话（如果有内容且不是查看历史记录）
        if (messages.value.length > 1) {
            try {
                await saveCurrentConversation();
                console.log('已保存当前对话到历史记录');
            } catch (error) {
                console.error('保存当前对话失败:', error);
            }
        }
    }

    // 清空消息列表，准备开始新对话
    messages.value = [];

    // 重置参考文献和参考文档数据
    latestReferenceChunks.value = [];
    latestDocAggs.value = [];
    console.log('已重置参考文献和参考文档数据');

    // 创建新会话
    console.log('左侧知识库切换，创建新会话');
    await createSession();
    console.log('创建会话完成，会话ID:', sessionId.value);

    // 添加代码防止页面自动滚动
    window.setTimeout(() => {
        // 恢复页面滚动位置到顶部，防止自动滚动到底部
        window.scrollTo({
            top: 0,
            behavior: 'auto'
        });
    }, 50);
};

// 新增加载知识库文件的方法，统一处理文件加载逻辑
const loadKnowledgeBaseFiles = async (repoId) => {
    try {
        // 设置加载状态
        loading.value = true;

        // 调用getFileList接口
        const params = {
            repoId: repoId, // 如果是单位知识库(全部)，则传null
            pageNum: 1,
            pageSize: 10
        };

        console.log('加载知识库文件，参数:', params);
        const res = await getFileList(params);
        console.log('知识库文件加载结果:', res);

        if (res.code == 0) {
            // 清空文件列表，防止显示旧数据
            fileList.value = [];

            // 在下一个事件循环中更新文件列表，确保DOM完全刷新
            setTimeout(() => {
                if (res.data && res.data.list) {
                    // 使用API返回的新数据
                    fileList.value = res.data.list;

                    // 更新是否有更多文件的状态
                    noMoreFiles.value = res.data.list.length < params.pageSize;

                    console.log('已加载文件数量:', fileList.value.length);

                    // 检查第一个文件的数据格式
                    if (fileList.value.length > 0) {
                        console.log('第一个文件数据:', fileList.value[0]);
                    }

                    // 检查是否有处理中的文件，如果有则启动轮询
                    if (hasProcessingFiles()) {
                        console.log('检测到处理中的文件，启动轮询...');
                        startFileStatusPolling();
                    } else {
                        console.log('没有处理中的文件，确保轮询已停止');
                        stopFileStatusPolling();
                    }
                } else {
                    fileList.value = [];
                    noMoreFiles.value = true;
                    // 确保轮询已停止
                    stopFileStatusPolling();
                }
            }, 0);

            // 显示成功提示
            let kbName = repoId ? '知识库' : (activeMenuGroup.value === 'public' ? '公共知识库广场' : '单位知识库');
            // showToast(`已加载${kbName}文件 (ID: ${repoId || '全部'})`);
            console.log('已加载知识库文件，知识库ID:', repoId);
        } else {
            // 显示错误提示
            // showToast(res.msg || '加载知识库文件失败', 'error');
            fileList.value = [];
        }
    } catch (error) {
        console.error('加载知识库文件失败:', error);
        // showToast('加载知识库文件失败，请重试', 'error');
        fileList.value = [];
    } finally {
        loading.value = false;
    }
};

// 在 script setup 部分添加文件列表相关代码
const fileListContainer = ref(null);
const loading = ref(false);
const isRefreshing = ref(false);
const noMoreFiles = ref(false);
const fileList = ref([]);

// 滚动处理函数
const handleFileListScroll = (event) => {
    const el = event.target;
    // 检测是否下拉刷新（向下滚动超过顶部）
    if (el.scrollTop < -50 && !isRefreshing.value) {
        refreshFiles();
    }
};

// 刷新文件列表
const refreshFiles = async () => {
    if (isRefreshing.value) return;

    isRefreshing.value = true;

    try {
        // 获取当前知识库ID
        let currentRepoId = null;

        // 如果是特定知识库
        if (activeMenuItem.value && activeMenuItem.value.startsWith('repo_')) {
            currentRepoId = activeMenuItem.value.split('_')[1];
        }
        // 如果是单位知识库(全部)
        else if (activeMenuGroup.value === 'unit' && activeMenuItem.value === 'all') {
            currentRepoId = null;
            console.log('刷新文件列表：单位知识库使用null作为ID');
        }

        // 根据当前知识库ID调用getFileList接口
        const params = {
            repoId: currentRepoId, // 如果是单位知识库(全部)，则传null
            pageNum: 1,
            pageSize: 10
        };

        console.log('刷新文件列表，参数:', params);
        const res = await getFileList(params);
        console.log('刷新文件列表响应:', res);

        if (res.code == 0) {
            // 重置文件列表状态
            noMoreFiles.value = false;

            // 更新文件列表
            if (res.data && res.data.list) {
                // 直接使用API返回的新数据替换现有数据
                fileList.value = res.data.list;
                noMoreFiles.value = res.data.list.length < params.pageSize;
            } else {
                fileList.value = [];
                noMoreFiles.value = true;
            }

            // 显示成功提示
            showToast('文件列表已刷新');
        } else {
            console.error('刷新文件列表失败:', res.msg);
            showToast(res.msg || '刷新文件列表失败', 'error');
        }
    } catch (error) {
        console.error('刷新文件列表失败:', error);
        showToast('刷新失败，请重试', 'error');
    } finally {
        isRefreshing.value = false;
    }
};

// 加载更多文件
const loadMoreFiles = async () => {
    if (loading.value || noMoreFiles.value) return;

    loading.value = true;

    try {
        // 计算下一页的页码
        const currentCount = fileList.value.length;
        const pageSize = 10; // 每页记录数
        const nextPage = Math.floor(currentCount / pageSize) + 1;

        // 获取当前知识库ID
        let currentRepoId = null;

        // 如果是特定知识库
        if (activeMenuItem.value && activeMenuItem.value.startsWith('repo_')) {
            currentRepoId = activeMenuItem.value.split('_')[1];
        }
        // 如果是单位知识库(全部)
        else if (activeMenuGroup.value === 'unit' && activeMenuItem.value === 'all') {
            currentRepoId = null;
            console.log('加载更多文件：单位知识库使用null作为ID');
        }

        // 构建请求参数
        const params = {
            repoId: currentRepoId, // 如果是单位知识库(全部)，则传null
            pageNum: nextPage,
            pageSize: pageSize
        };

        console.log('加载更多文件，参数:', params);
        const res = await getFileList(params);
        console.log('加载更多文件响应:', res);

        if (res.code == 0 && res.data && res.data.list) {
            if (res.data.list.length > 0) {
                // 将新加载的文件追加到文件列表中
                fileList.value = [...fileList.value, ...res.data.list];

                // 如果返回的文件数少于请求的数量，说明没有更多文件了
                noMoreFiles.value = res.data.list.length < pageSize;
            } else {
                // 没有更多文件
                noMoreFiles.value = true;
            }

            console.log('文件列表更新后数据量:', fileList.value.length);
        } else {
            // 加载失败或没有更多数据
            noMoreFiles.value = true;
            console.log('加载更多文件失败或没有更多文件');
        }
    } catch (error) {
        console.log('暂无更多文件:');
        showToast('暂无更多文件');
    } finally {
        loading.value = false;
    }
};

// 设置默认选中的知识库
const selectedKnowledgeBase = ref({
    text: '单位知识库（全部）',
    value: 'unit'
});

// 创建计算属性，动态生成知识库选项
const knowledgeBaseOptions = computed(() => {
    // 基础选项
    const baseOptions = [
        {
            text: '单位知识库（全部）',
            value: 'unit'
        }
    ];

    // 从左侧菜单的repoList添加知识库选项
    const repoOptions = repoList.value.map(repo => ({
        text: repo.repoDesc,
        value: 'repo_' + repo.repoId
    }));

    // 合并基础选项和知识库选项
    return [...baseOptions, ...repoOptions];
});

// 处理知识库选择变化事件
const handleKnowledgeBaseChange = async (value) => {
    console.log('知识库选择变化事件触发:', value);

    // 打印完整的选择项数据
    console.log('选择的知识库完整数据:', value);

    // 同步更新左侧菜单的选中状态
    activeMenuGroup.value = 'unit';

    // 如果是单位知识库
    if (value && value.value === 'unit') {
        activeMenuItem.value = 'all';
        repoIds.value = null;
    }
    // 如果是特定知识库
    else if (value && value.value && value.value.startsWith('repo_')) {
        const repoId = parseInt(value.value.split('_')[1]);
        activeMenuItem.value = 'repo_' + repoId;
        repoIds.value = repoId;

        const selectedRepo = repoList.value.find(repo => repo.repoId == repoId);
        console.log('选择的知识库详细数据:', selectedRepo);
    }

    // 保存知识库选择状态到localStorage
    saveKnowledgeBaseState();

    // 打印所有相关数据
    console.log('选择变化详细数据:', {
        selectedValue: value.value,
        selectedText: value.text,
        allOptions: knowledgeBaseOptions.value,
        repoList: repoList.value,
        currentRepoIds: repoIds.value,
        activeMenuGroup: activeMenuGroup.value,
        activeMenuItem: activeMenuItem.value
    });

    // 检查是否正在查看历史记录，如果是则不创建新会话
    if (selectedHistoryIndex.value !== -1) {
        console.log('正在查看历史记录，不创建新会话');
        return;
    }

    // 在切换知识库时处理变更
    console.log('知识库切换，处理知识库变更');

    // 保存当前对话（如果有内容）
    if (messages.value.length > 1) {
        try {
            await saveCurrentConversation();
            console.log('已保存当前对话到历史记录');
        } catch (error) {
            console.error('保存当前对话失败:', error);
        }
    }

    // 清空消息列表，准备开始新对话
    messages.value = [];

    // 重置参考文献和参考文档数据
    latestReferenceChunks.value = [];
    latestDocAggs.value = [];
    console.log('已重置参考文献和参考文档数据');

    // 创建新会话
    console.log('问答区域知识库切换，创建新会话');
    await createSession();
    console.log('创建会话完成，会话ID:', sessionId.value);

    // 如果切换了知识库，加载对应的文件列表
    if (value.value === 'unit') {
        await loadKnowledgeBaseFiles(null);
    } else if (value.value.startsWith('repo_')) {
        const repoId = value.value.split('_')[1];
        await loadKnowledgeBaseFiles(repoId);
    }
};

// 监听知识库选择变化 - 问答区域的选择不影响左侧菜单和文件列表
watch(selectedKnowledgeBase, (newValue) => {
    console.log('问答区域知识库选择变化:', newValue);
    console.log('知识库选择数据详情:', {
        selectedValue: newValue.value,
        selectedText: newValue.text,
        allOptions: knowledgeBaseOptions.value,
        repoList: repoList.value
    });
    // 这里不再有任何影响左侧菜单或文件列表的代码
}, { immediate: true });

// 在script setup部分添加currentKnowledgeBase变量
const currentKnowledgeBase = ref('全部');

// 添加获取当前知识库名称的函数
// 打印知识库详细数据的函数
const printRepoDetails = (repo) => {
    console.log('知识库完整数据 (右键菜单):', repo);
    console.log('知识库详细属性 (右键菜单):', {
        repoId: repo.repoId,
        repoDesc: repo.repoDesc,
        remark: repo.remark,
        isJoined: repo.isJoined,
        operationPermission: repo.operationPermission,
        createTime: repo.createTime,
        updateTime: repo.updateTime,
        createBy: repo.createBy,
        updateBy: repo.updateBy,
        所有属性: Object.keys(repo)
    });

    // 显示提示
    ElMessage({
        message: `已在控制台打印知识库 "${repo.repoDesc}" 的详细数据`,
        type: 'info',
        duration: 2000
    });
};

const getCurrentKnowledgeBaseName = () => {
    // 如果activeMenuItem是以repo_开头，表示是动态生成的知识库
    if (activeMenuItem.value && activeMenuItem.value.startsWith('repo_')) {
        const repoId = activeMenuItem.value.split('_')[1];
        const repo = repoList.value.find(r => r.repoId == repoId);
        if (repo) {
            return repo.repoDesc || '未命名知识库';
        }
    }

    // 根据当前选中的菜单项返回对应的知识库名称
    switch (activeMenuItem.value) {
        case 'create':
            return '全部';
        case 'unfinished':
            return '未完成人才知识库';
        case 'comprehensive':
            return '综合科知识库';
        case 'civilization':
            return '新时代文明实践科知识库';
        case 'promotion':
            return '宣传培育科知识库';
        case 'grassroot':
            return '基层工作科知识库';
        case 'party':
            return '党支部知识库';
        case 'spiritual':
            return '精神文明建设科知识库';
        default:
            return currentKnowledgeBase.value || '全部知识库';
    }
};

// 添加前进和后退页面的函数
const prevPage = () => {
    if (currentPage.value > 1) {
        currentPage.value--;
    }
};

const nextPage = () => {
    if (currentPage.value < Math.ceil(totalMembersCount.value / pageSize.value)) {
        currentPage.value++;
    }
};
const selectedMembers = ref([]);
// 添加删除成员函数和计算属性
const hasSelectedMembers = computed(() => {
    return selectedMembers.value.length > 0;
});

// 删除选中的成员
const deleteSelectedMembers = async () => {
    if (!hasSelectedMembers.value) {
        return;
    }

    // 获取选中成员的ID
    const selectedIds = selectedMembers.value.map(member => member.id);

    if (selectedIds.length == 0) {
        showToast('请选择要删除的成员', 'warning');
        return;
    }

    try {
        // 显示确认对话框
        proxy.$modal.confirm(`确定要删除选中的${selectedIds.length}个成员吗?`).then(async () => {
            isLoading.value = true;

            // 调用MembersRemove接口删除成员，将成员id数组转为逗号分隔的字符串
            const res = await MembersRemove({
                ids: selectedIds.join(',')
            });

            if (res.code == 0) {
                // 显示提示
                showToast(`已删除${selectedIds.length}个成员`, 'success');

                // 重置全选状态
                selectAllMembers.value = false;

                // 重新加载成员列表
                await fetchMembersList();

                // 重新加载左侧知识库列表
                await getLeftList();
            } else {
                showToast(res.msg || '删除失败', 'error');
            }
        }).catch(() => {
            // 用户取消删除操作
        }).finally(() => {
            isLoading.value = false;
        });
    } catch (error) {
        console.error('批量删除成员失败:', error);
        showToast('批量删除成员失败', 'error');
        isLoading.value = false;
    }
};

// 添加用户对话框相关变量
const showAddUserDialog = ref(false);
const userSearchQuery = ref({
    name: '',
    phone: ''
});
const newUser = ref({
    selectedUser: null,
    remark: ''
});

// 分页相关
const currentUserPage = ref(1);
const userPageSize = ref(10);
const totalUsers = ref(0);
const totalUserPages = ref(1);
const goToUserPageInput = ref(1);

// 用户列表数据
const userSelectOptions = ref([]);
const selectAllUsers = ref(false);

// 搜索用户
const searchUsers = async () => {
    currentUserPage.value = 1;
    await fetchUserSelectOptions();
};

// 重置搜索
const resetUserSearch = async () => {
    userSearchQuery.value.name = '';
    userSearchQuery.value.phone = '';
    currentUserPage.value = 1;
    await fetchUserSelectOptions();
};

// 全选/取消全选
const toggleSelectAllUsers = () => {
    filteredUsers.value.forEach(user => {
        user.selected = selectAllUsers.value;
    });
};

// 获取用户列表数据
const fetchUserSelectOptions = async () => {
    try {
        const params = {
            pageNum: currentUserPage.value,
            pageSize: userPageSize.value,
            status: '0', // 只获取状态正常的用户
            nickName: userSearchQuery.value.name || undefined,
            phonenumber: userSearchQuery.value.phone || undefined,
            repoId: currentRepoId.value // 添加知识库ID参数
        };

        const res = await userListApi(params);

        if (res.code == 0) {
            // 为每个用户添加selected属性
            userSelectOptions.value = res.data.list.map(user => ({
                ...user,
                selected: false
            }));
            totalUsers.value = res.data.total || 0;
            totalUserPages.value = Math.ceil(totalUsers.value / userPageSize.value);
        } else {
            console.error('获取用户列表失败');
            userSelectOptions.value = [];
            totalUsers.value = 0;
            totalUserPages.value = 0;
        }
    } catch (error) {
        console.error('获取用户列表出错:', error);
        userSelectOptions.value = [];
        totalUsers.value = 0;
        totalUserPages.value = 0;
    }
};

// 分页操作
const prevUserPage = () => {
    if (currentUserPage.value > 1) {
        currentUserPage.value--;
        fetchUserSelectOptions();
    }
};

const nextUserPage = () => {
    if (currentUserPage.value < totalUserPages.value) {
        currentUserPage.value++;
        fetchUserSelectOptions();
    }
};

const goToUserPage = (page) => {
    currentUserPage.value = page;
    fetchUserSelectOptions();
};

const changePageSize = (size) => {
    userPageSize.value = size;
    currentUserPage.value = 1;
    fetchUserSelectOptions();
};

// 过滤用户列表
const filteredUsers = computed(() => {
    return userSelectOptions.value;
});

// 判断是否有选中的用户
const hasSelectedUsers = computed(() => {
    return userSelectOptions.value.some(user => user.selected);
});

// 处理用户页面变化
const handleUserPageChange = (page) => {
    currentUserPage.value = page;
    fetchUserSelectOptions();
};

// 确认添加用户
const confirmAddUser = async () => {
    // 获取所有选中的用户
    const selectedUsers = userSelectOptions.value.filter(user => user.selected);

    if (selectedUsers.length === 0) {
        showToast('请至少选择一个用户', 'error');
        return;
    }

    // 确保有当前知识库ID
    if (!currentRepoId.value) {
        // 尝试再次获取当前知识库ID
        const repo = repoList.value.find(repo => repo.repoDesc === currentKnowledgeBase.value);
        if (repo) {
            currentRepoId.value = repo.repoId;
        } else {
            // showToast('未找到当前知识库ID，无法添加成员', 'error');
            return;
        }
    }

    try {
        // 构建批量添加用户的参数数组
        const batchUsers = selectedUsers.map(user => ({
            userId: user.userId,
            repoId: currentRepoId.value,
            repoName: currentKnowledgeBase.value
        }));

        console.log('批量添加成员参数:', batchUsers);

        // 调用批量添加成员API
        const res = await MembersAddBatch(batchUsers);

        if (res.code == 0) {
            // 关闭对话框
            showAddUserDialog.value = false;

            // 显示提示
            showToast(`成功添加${selectedUsers.length}个成员`, 'success');

            // 重新加载成员列表
            fetchMembersList();

            // 重新加载左侧知识库列表
            await getLeftList();

            // 重置表单
            resetUserSearch();
            newUser.value.remark = '';
        } else {
            // showToast(res.msg || '添加成员失败', 'error');
        }
    } catch (error) {
        console.error('添加用户失败:', error);
        // showToast('添加用户失败，请重试', 'error');
    }
};

// 格式化日期
const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 获取随机颜色
const getRandomColor = () => {
    const colors = ['purple', 'cyan', 'orange', 'teal', 'blue', 'pink', 'red', 'green', 'blue-grey', 'deep-purple'];
    return colors[Math.floor(Math.random() * colors.length)];
};

// 在script setup部分添加全选功能
// 添加全选变量
const selectAllFiles = ref(false);

// 添加可选择文件的计算属性
const selectableFiles = computed(() => {
    return fileList.value.filter(file => {
        // 排除上传中的文件
        if (file.status === 'UPLOADING') return false;

        // 已解析成功的文件可选
        if (file.status === 'PARSE_SUCCESS') return true;

        // 解析中的文件：对于最高级管理员、知识库管理员和自己上传的文件可选
        if (file.status === 'PARSING') {
            return isHighLevelAdmin.value ||
                (isCurrentRepoAdmin.value && isJoinedCurrentRepo.value) ||
                file.userId === userStore.id;
        }

        // 解析失败的文件：根据权限判断
        if (file.status === 'PARSE_FAILED') {
            return isHighLevelAdmin.value ||
                (isCurrentRepoAdmin.value && isJoinedCurrentRepo.value) ||
                file.userId === userStore.id;
        }

        // 未解析的文件：根据权限判断
        if (file.status === 'UNPARSED') {
            return isHighLevelAdmin.value ||
                (isCurrentRepoAdmin.value && isJoinedCurrentRepo.value) ||
                file.userId === userStore.id;
        }

        return false;
    });
});

// 全选/取消全选功能
const toggleSelectAllFiles = () => {
    // 对所有可选择的文件进行操作
    const filesToToggle = selectableFiles.value;
    filesToToggle.forEach(file => {
        file.selected = selectAllFiles.value;
    });
};

// 监听文件选中状态变化
watch(
    () => fileList.value.map(file => file.selected),
    () => {
        // 检查可选择的文件状态
        const availableFiles = selectableFiles.value;
        if (availableFiles.length === 0) {
            // 没有可选择的文件，不设置全选状态
            selectAllFiles.value = false;
        } else {
            // 计算选中的文件数量
            const selectedCount = availableFiles.filter(file => file.selected).length;

            // 如果所有可选择的文件都被选中，设置为全选状态
            if (selectedCount === availableFiles.length) {
                selectAllFiles.value = true;
            }
            // 如果部分文件被选中，设置为null（半选中状态）
            else if (selectedCount > 0) {
                selectAllFiles.value = null;
            }
            // 如果没有文件被选中，取消全选状态
            else {
                selectAllFiles.value = false;
            }
        }
    },
    { deep: true }
);

// 添加计算属性判断是否有文件被选中
const hasSelectedFiles = computed(() => {
    return fileList.value.some(file => file.selected);
});

// 检查是否有自己上传的共享文件被选中
const hasAnySharedFilesSelected = computed(() => {
    // 如果选中的文件中有自己上传的共享文件，显示取消共享按钮
    return fileList.value.some(file =>
        file.selected &&
        file.permissionType === 'ALL' &&
        file.userId == userStore.id
    );
});

// 检查是否有自己上传的私有文件被选中
const hasAnyPrivateFilesSelected = computed(() => {
    // 如果选中的文件中有自己上传的私有文件，显示分享按钮
    return fileList.value.some(file =>
        file.selected &&
        file.permissionType === 'REPO' &&
        file.userId == userStore.id
    );
});

// 在script setup部分添加上传文件相关的变量和方法
const showUploadDialog = ref(false);
const isDragOver = ref(false);
const fileInput = ref(null);
const selectedFiles = ref([]);

// 文档切片对话框相关
const showSlicingDialog = ref(false);
const currentSlicingFile = ref(null);

const parserOptions = ref([]);
const loadingParsers = ref(false);
const processingSlicing = ref(false);
const isUploading = ref(false);
const uploadedCount = ref(0);
// 添加文件权限类型变量，默认设置为共享(ALL)
// 如果用户有部门ID且加入了知识库(isJoin为true)，selectMenuItem函数会将其重置为REPO
const filePermissionType = ref('ALL');

// 监听filePermissionType变化，同步到window对象以便store可以访问
watch(filePermissionType, (newValue) => {
    window.filePermissionType = newValue;
    console.log('文件权限类型已更新:', newValue);
});
// 初始化window对象中的权限类型，默认为共享(ALL)
window.filePermissionType = 'ALL';

// 上传文件
const uploadFiles = async () => {
    if (selectedFiles.value.length === 0) {
        showToast('请先选择文件', 'error');
        return;
    }

    // 再次验证文件格式
    const allowedExtensions = ["docx", "xlsx", "xls", "ppt", "pptx", "pdf", "txt", "jpeg", "jpg", "png", "tif", "gif", "csv", "json", "eml", "html", "doc"];
    const file = selectedFiles.value[0];
    const fileExtension = file.name.split('.').pop().toLowerCase();

    if (!allowedExtensions.includes(fileExtension)) {
        showToast(`不支持的文件格式: ${fileExtension}，请上传以下格式的文件：${allowedExtensions.join(', ')}`, 'error');
        return;
    }

    isUploading.value = true;
    showUploadDialog.value = false;
    uploadedCount.value = 0;

    try {
        // 获取当前知识库ID
        let currentRepoIdValue = null;
        // 确定当前是否在单位知识库
        const isUnitKnowledgeBase = activeMenuGroup.value === 'unit';

        if (!isUnitKnowledgeBase) {
            // 如果不是单位知识库，获取当前知识库ID
            if (activeMenuItem.value && activeMenuItem.value.startsWith('repo_')) {
                currentRepoIdValue = activeMenuItem.value.split('_')[1];
            }
        }

        // 获取要上传的文件
        const file = selectedFiles.value[0];

        // 添加文件格式验证
        const allowedExtensions = ["docx", "xlsx", "xls", "ppt", "pptx", "pdf", "txt", "jpeg", "jpg", "png", "tif", "gif", "csv", "json", "eml", "html", "doc"];
        const fileExtension = file.name.split('.').pop().toLowerCase();

        if (!allowedExtensions.includes(fileExtension)) {
            showToast(`不支持的文件格式: ${fileExtension}，请上传以下格式的文件：${allowedExtensions.join(', ')}`, 'error');
            return;
        }

        // 创建上传文件对象，但暂不添加到列表
        const uploadingFile = {
            id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            fileName: file.name,
            name: file.name,
            size: formatFileSize(file.size),
            date: new Date().toLocaleString(),
            uploader: '当前用户',
            description: '',
            tags: [],
            selected: false,
            status: 'UPLOADING',
            progress: 0,
            originalFile: file
        };

        // 先不将文件添加到列表，等待上传成功后再添加
        // 同时记录到uploadStore，以便处理状态
        uploadStore.addUploadingFile(file);

        // 显示正在上传的提示
        showToast(`正在上传文件: ${file.name}`, 'info');

        try {
            // 创建FormData对象
            var formData = new FormData();
            formData.append("file", file);

            // 获取当前知识库的remark字段作为datasetId
            let currentDatasetId = '';
            if (repoIds.value) {
                const currentRepo = repoList.value.find(r => r.repoId == repoIds.value);
                if (currentRepo && currentRepo.remark) {
                    currentDatasetId = currentRepo.remark;
                    console.log('上传文件使用知识库remark字段作为datasetId:', currentDatasetId);
                }
            }

            // 使用upFile接口上传文件，并添加进度回调1
            console.log('上传文件，知识库ID:', repoIds.value, '文件权限:', filePermissionType.value);
            const response = await upFile(formData, (progressEvent) => {
                if (progressEvent.lengthComputable) {
                    // 更新上传进度 - 限制在0-50%范围内，留出更多空间给后续处理，使整体进度更慢
                    const actualPercent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                    // 使用0.5的系数而不是0.7，让进度更慢
                    const displayPercent = Math.min(Math.round(actualPercent * 0.5), 50);

                    console.log('上传进度:', actualPercent + '%', '显示进度:', displayPercent + '%', '已上传:', progressEvent.loaded, '总大小:', progressEvent.total);

                    // 仅记录进度，不更新UI，因为文件还未添加到列表
                    uploadingFile.progress = displayPercent;

                    // 如果实际进度超过5%但显示进度没有变化超过2秒，启动自动进度
                    if (actualPercent > 5) {
                        window['lastProgressTime'] = window['lastProgressTime'] || Date.now();
                        window['lastProgressValue'] = window['lastProgressValue'] || 0;

                        // 如果进度超过2秒没有明显变化，更新时间和值
                        if (Math.abs(window['lastProgressValue'] - displayPercent) >= 1) {
                            window['lastProgressTime'] = Date.now();
                            window['lastProgressValue'] = displayPercent;
                        }
                    }
                }
            }, repoIds.value, filePermissionType.value).catch(error => {
                // 处理上传超时或失败
                console.error('文件上传失败:', error);

                // 清除所有计时器
                if (window['autoProgress_' + uploadingFile.id]) {
                    clearInterval(window['autoProgress_' + uploadingFile.id]);
                    window['autoProgress_' + uploadingFile.id] = null;
                }

                // 从uploadStore中移除文件
                const uploadingFileId = uploadStore.uploadingFiles.find(f =>
                    f.fileName === file.name &&
                    Math.abs(f.originalFile.size - file.size) < 10
                )?.id;

                if (uploadingFileId) {
                    uploadStore.removeUploadingFile(uploadingFileId);
                }

                throw error; // 继续向上抛出错误，让外层catch捕获
            });
            console.log('文件信息', response, file);

            // 打印最原始的响应数据
            console.log('上传文件原始响应数据:', JSON.stringify(response.data));
            console.log('上传文件完整响应对象:', response);

            // 检查upFile接口返回的code是否为0，如果不是则直接显示上传失败并返回
            if (response.code !== 0) {
                console.error('upFile接口返回错误:', response.msg || '上传失败');

                // 清除自动进度递增定时器
                if (window['autoProgress_' + uploadingFile.id]) {
                    clearInterval(window['autoProgress_' + uploadingFile.id]);
                    window['autoProgress_' + uploadingFile.id] = null;
                }

                // 清除其他所有计时器
                clearInterval(window['progressInterval_' + uploadingFile.id]);
                clearInterval(window['dotsAnimation_' + uploadingFile.id]);

                // 显示错误消息
                showToast(response.msg || '文件上传失败', 'error');
                return; // 直接返回，不执行后续代码
            }

            // 上传成功，此时才将文件添加到文件列表
            // 将上传中的文件添加到文件列表顶部
            uploadingFile.status = 'PARSING'; // 确保状态为解析中，而不是上传失败
            fileList.value = [uploadingFile, ...fileList.value];
            console.log('文件上传成功，添加到列表:', uploadingFile.name);

            // 启动自动递增定时器，保证进度条显示
            if (!window['autoProgress_' + uploadingFile.id]) {
                console.log('启动自动进度递增');
                window['autoProgress_' + uploadingFile.id] = setInterval(() => {
                    const currentIdx = fileList.value.findIndex(item => item.id === uploadingFile.id);
                    if (currentIdx !== -1) {
                        const currentFile = fileList.value[currentIdx];
                        // 如果文件上传状态改变或者进度已经大于98%，清除定时器
                        if (currentFile.status !== 'UPLOADING' || currentFile.progress >= 98) {
                            clearInterval(window['autoProgress_' + uploadingFile.id]);
                            window['autoProgress_' + uploadingFile.id] = null;
                            console.log('清除自动进度递增定时器');
                        } else {
                            // 根据当前进度确定增加速度
                            let increment = 0.8; // 默认增长率更快
                            const currentProgress = currentFile.progress;

                            // 进度越高，增加越慢
                            if (currentProgress > 80) increment = 0.1; // 80%后显著减慢
                            else if (currentProgress > 50) increment = 0.5; // 50-80%之间中等速度

                            // 确保不会超过98%，为最终完成留出空间
                            const newProgress = Math.min(currentProgress + increment, 98);
                            fileList.value[currentIdx].progress = newProgress;

                            // 强制视图更新 - 创建对象副本
                            fileList.value[currentIdx] = { ...fileList.value[currentIdx] };

                            console.log('自动增加进度到:', newProgress.toFixed(2) + '%');
                        }
                    } else {
                        // 如果找不到文件，也要清除定时器
                        clearInterval(window['autoProgress_' + uploadingFile.id]);
                        window['autoProgress_' + uploadingFile.id] = null;
                        console.log('找不到文件，清除自动进度递增定时器');
                    }
                }, 300); // 缩短间隔，确保进度更新更流畅
            }

            // 详细打印响应数据结构
            console.log('响应数据结构:');
            if (response.data) {
                console.log('- response.data:', response.data);
                if (response.data.data) {
                    console.log('- response.data.data:', response.data.data);
                    if (Array.isArray(response.data.data)) {
                        console.log('  (数组长度:', response.data.data.length, ')');
                        response.data.data.forEach((item, idx) => {
                            console.log(`  - [${idx}]:`, item);
                        });
                    }
                }
            }

            // 检查并提取需要的数据
            let responseData = {};
            if (response.data && response.data.data) {
                if (Array.isArray(response.data.data) && response.data.data.length > 0) {
                    // 如果是数组，取第一个元素
                    responseData = response.data.data[0];
                    console.log('响应数据是数组，使用第一项:', responseData);
                } else {
                    // 如果不是数组，直接使用
                    responseData = response.data.data;
                    console.log('响应数据是对象:', responseData);
                }
            }

            // 检查响应数据中的各个字段
            console.log('提取的响应数据字段:');
            console.log('- dataset_id:', responseData.dataset_id);
            console.log('- id:', responseData.id);
            console.log('- type:', responseData.type);
            console.log('- md5:', responseData.md5);
            console.log('- creator:', responseData.creator);
            console.log('- userId:', responseData.userId);

            // 获取文件扩展名
            const fileExt = file.name.split('.').pop() || '';

            // 获取dataset_id - 这是文档ID
            const dataset_id = responseData.dataset_id || '';

            // 构建文件路径
            const filePath = `/document/${dataset_id}?ext=${fileExt}&prefix=document`;

            // 更新文件状态为部分完成，准备进入解析阶段
            const index = fileList.value.findIndex(item => item.id == uploadingFile.id);
            if (index !== -1) {
                // 不要在这里设为PARSE_SUCCESS，这会导致状态跳过PARSING
                // 不要在这里设置进度为100%，会造成进度回退
                fileList.value[index].progress = 50; // 保持在50%，表示文件上传完成但解析未完成
            }

            // 获取当前用户ID
            const currentUserId = useUserStore().id;
            console.log('当前用户ID:', currentUserId);

            // 更新文件状态为解析中
            const fileIndex = fileList.value.findIndex(item => item.id == uploadingFile.id);
            if (fileIndex !== -1) {
                fileList.value[fileIndex].status = 'PARSING';
                // 进度从50%逐渐增加到75% (修改为更慢的增长)
                const progressInterval = setInterval(() => {
                    if (fileList.value[fileIndex].progress < 75) {
                        // 减慢增长速度，每次只增加0.5%
                        fileList.value[fileIndex].progress += 0.5;
                    } else {
                        clearInterval(progressInterval);
                    }
                }, 300); // 增加间隔时间，从200ms变为300ms
            }

            // 确保进度条已经设置为50%，表示开始解析
            const currentFileIndex = fileList.value.findIndex(item => item.id == uploadingFile.id);
            if (currentFileIndex !== -1 && fileList.value[currentFileIndex].progress < 50) {
                fileList.value[currentFileIndex].progress = 50;
            }

            // 更新文件状态为解析中
            const fileIndex2 = fileList.value.findIndex(item => item.id == uploadingFile.id);
            if (fileIndex2 !== -1) {
                // 清除之前的计时器
                clearInterval(window['progressInterval_' + uploadingFile.id]);

                // 进度从75%逐渐增加到98%，预留最后的拼命上传中状态
                window['progressInterval_' + uploadingFile.id] = setInterval(() => {
                    if (fileList.value[fileIndex2].progress < 98) {
                        // 减慢增长速度，根据当前进度动态调整增量
                        const currentProgress = fileList.value[fileIndex2].progress;
                        let increment = 0.3; // 基础增量很小

                        // 越接近98%，增量越小
                        if (currentProgress > 90) {
                            increment = 0.1;
                        } else if (currentProgress > 85) {
                            increment = 0.2;
                        }

                        fileList.value[fileIndex2].progress += increment;
                    } else {
                        // 到达98%后，切换到拼命上传中状态
                        clearInterval(window['progressInterval_' + uploadingFile.id]);

                        // 设置为99%并显示拼命上传中
                        fileList.value[fileIndex2].progress = 99;

                        // 创建动画效果，定期更新文本以显示动画点
                        window['dotsAnimation_' + uploadingFile.id] = setInterval(() => {
                            const dots = '.'.repeat(1 + Math.floor(Date.now() / 500) % 3); // 动态生成1-3个点
                            // 更新文件列表中的显示，强制重新渲染
                            const idx = fileList.value.findIndex(item => item.id === uploadingFile.id);
                            if (idx !== -1) {
                                // 触发视图更新但不改变进度值
                                fileList.value[idx] = { ...fileList.value[idx] };
                            }
                        }, 500); // 每500毫秒更新一次
                    }
                }, 350); // 增加间隔时间，让进度增长更慢
            }

            // 清除所有进度计时器
            clearInterval(window['progressInterval_' + uploadingFile.id]);
            clearInterval(window['dotsAnimation_' + uploadingFile.id]); // 清除动画点计时器

            // 清除自动进度递增定时器
            if (window['autoProgress_' + uploadingFile.id]) {
                clearInterval(window['autoProgress_' + uploadingFile.id]);
                window['autoProgress_' + uploadingFile.id] = null;
            }

            // 从uploadStore中移除文件，确保不会阻止切换知识库
            const uploadingFileId = uploadStore.uploadingFiles.find(f =>
                f.fileName === file.name &&
                Math.abs(f.originalFile.size - file.size) < 10
            )?.id;

            if (uploadingFileId) {
                uploadStore.removeUploadingFile(uploadingFileId);
                console.log('从uploadStore中移除文件');
            }

            // 立即检查并更新isUploading状态
            uploadStore.isUploading = uploadStore.uploadingFiles.some(f =>
                f.status === 'UPLOADING' || f.status === 'PARSING'
            );

            uploadedCount.value++;

            // 设置进度为100%并更新状态
            const finalIndex = fileList.value.findIndex(item => item.id == uploadingFile.id);
            if (finalIndex !== -1) {
                fileList.value[finalIndex].progress = 100;
                fileList.value[finalIndex].status = 'PARSING';
            }

            // 文件上传成功后，直接调用chunksDoc解析接口
            try {
                // 获取docId - 直接使用upFile接口返回的docId
                const docId = response.data && response.data.docId ?
                    response.data.docId : responseData.id;

                // 获取datasetId - 从上传响应中获取
                const chunkDatasetId = response.data && response.data.datasetId ?
                    response.data.datasetId : responseData.datasetId;

                if (docId && chunkDatasetId) {
                    // 调用chunksDoc接口处理文件
                    const chunksData = {
                        ids: [docId],
                        datasetId: chunkDatasetId
                    };

                    console.log('调用chunksDoc接口处理文件，参数:', chunksData);
                    const chunksResponse = await chunksDoc(chunksData);
                    console.log('chunksDoc接口响应:', chunksResponse);

                    if (chunksResponse && chunksResponse.code === 0) {
                        console.log('文件chunksDoc处理成功');
                    } else {
                        console.warn('文件chunksDoc处理返回异常:', chunksResponse);
                    }
                } else {
                    console.warn('未找到有效的docId或datasetId，无法调用解析接口');
                    console.log('docId:', docId, 'chunkDatasetId:', chunkDatasetId);
                    console.log('response.data:', response.data);
                    console.log('responseData:', responseData);
                }
            } catch (chunkError) {
                console.error('调用文件解析接口出错:', chunkError);
                // 解析错误不影响上传成功提示
            }

            // 延迟一小段时间后显示成功提示，给用户时间看到100%
            setTimeout(() => {
                showToast(`文件上传成功`);
            }, 500);

            // 文件上传成功后，使用统一的文件加载方法刷新文件列表
            try {
                console.log('文件保存成功，准备刷新文件列表');

                // 延时调用，确保DOM更新完成
                setTimeout(async () => {
                    // 使用统一的加载方法，传入当前知识库ID
                    await loadKnowledgeBaseFiles(repoIds.value);

                    console.log('文件列表刷新完成');
                }, 800); // 延长时间确保DOM完全更新
            } catch (listError) {
                console.error('刷新文件列表出错:', listError);
                // 即使刷新出错，也不影响上传成功的提示
            }
        } catch (error) {
            console.error('上传文件失败:', error);

            // 清除自动进度递增定时器
            if (window['autoProgress_' + uploadingFile.id]) {
                clearInterval(window['autoProgress_' + uploadingFile.id]);
                window['autoProgress_' + uploadingFile.id] = null;
            }

            // 检查是否是超时错误
            if (error.message && error.message.includes('timeout')) {
                showToast('文件上传超时，请重试', 'error');
            } else {
                showToast('上传文件失败，请重试', 'error');
            }

            // 从uploadStore中移除文件
            const uploadingFileId = uploadStore.uploadingFiles.find(f =>
                f.fileName === file.name &&
                Math.abs(f.originalFile.size - file.size) < 10
            )?.id;

            if (uploadingFileId) {
                uploadStore.removeUploadingFile(uploadingFileId);
            }
        }
    } finally {
        isUploading.value = false;
        selectedFiles.value = [];
    }
};

// 打开上传对话框
const openUploadDialog = () => {
    // 首先检查权限
    if (!canShowUploadButton.value) {
        showToast('您没有上传文件的权限', 'error');
        return;
    }

    selectedFiles.value = [];
    // 如果用户没有加入当前知识库(isJoin为false)或没有部门ID，设置为共享
    if (!isJoin.value || !(userStore.users && userStore.users.deptId)) {
        filePermissionType.value = 'ALL';
    }
    // 不再在这里重置文件权限类型为REPO，保留之前设置的值

    // 同步到window对象
    window.filePermissionType = filePermissionType.value;
    console.log('打开上传对话框，设置权限类型为:', filePermissionType.value, '是否已加入:', isJoin.value);
    showUploadDialog.value = true;
};

// 触发文件选择
const triggerFileInput = () => {
    fileInput.value.click();
};

// 处理文件选择变化
const handleFileInputChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
        // 添加文件格式验证
        const allowedExtensions = ["docx", "xlsx", "xls", "ppt", "pptx", "pdf", "txt", "jpeg", "jpg", "png", "tif", "gif", "csv", "json", "eml", "html", "doc"];
        const file = files[0];
        const fileExtension = file.name.split('.').pop().toLowerCase();

        if (!allowedExtensions.includes(fileExtension)) {
            showToast(`不支持的文件格式: ${fileExtension}，请上传以下格式的文件：${allowedExtensions.join(', ')}`, 'error');
            // 重置input以允许选择相同文件
            e.target.value = '';
            return;
        }

        // 单文件上传，只取第一个文件
        selectedFiles.value = [files[0]];
    }
    // 重置input以允许选择相同文件
    e.target.value = '';
};

// 处理拖拽相关事件
const handleDragOver = (e) => {
    e.preventDefault();
    isDragOver.value = true;
};

const handleDragLeave = (e) => {
    e.preventDefault();
    isDragOver.value = false;
};

const handleDrop = (e) => {
    e.preventDefault();
    isDragOver.value = false;
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
        // 添加文件格式验证
        const allowedExtensions = ["docx", "xlsx", "xls", "ppt", "pptx", "pdf", "txt", "jpeg", "jpg", "png", "tif", "gif", "csv", "json", "eml", "html", "doc"];
        const file = files[0];
        const fileExtension = file.name.split('.').pop().toLowerCase();

        if (!allowedExtensions.includes(fileExtension)) {
            showToast(`不支持的文件格式: ${fileExtension}，请上传以下格式的文件：${allowedExtensions.join(', ')}`, 'error');
            return;
        }

        // 单文件上传，只取第一个文件
        selectedFiles.value = [files[0]];
    }
};
const cancel = () => {
    console.log('cancel', e)
    // message.value.error('Click on No')
}
const openChange = () => {
    // console.log('open', open)
}
// 移除选中的文件
const removeSelectedFile = (index) => {
    selectedFiles.value.splice(index, 1);
};

// 清空所有选中的文件
const clearSelectedFiles = () => {
    selectedFiles.value = [];
    // 如果用户没有加入当前知识库(isJoin为false)或没有部门ID，设置为共享
    if (!isJoin.value || !(userStore.users && userStore.users.deptId)) {
        filePermissionType.value = 'ALL';
    }
    // 不再在这里重置文件权限类型为REPO，保留之前设置的值

    // 同步到window对象
    window.filePermissionType = filePermissionType.value;
    console.log('清除选择的文件，设置权限类型为:', filePermissionType.value, '是否已加入:', isJoin.value);
};

// 格式化文件大小
const formatFileSize = (size) => {
    if (size === 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(size) / Math.log(1024));

    return (size / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
};

// 获取文件预览按钮提示文字
const getPreviewButtonText = (fileName) => {
    if (!fileName) return '查看文件';

    const lowerFileName = fileName.toLowerCase();

    if (lowerFileName.endsWith('.pdf')) {
        return '查看PDF预览';
    } else if (lowerFileName.endsWith('.doc') || lowerFileName.endsWith('.docx')) {
        return '查看Word预览';
    } else if (lowerFileName.endsWith('.xls') || lowerFileName.endsWith('.xlsx')) {
        return '查看Excel预览';
    } else if (lowerFileName.endsWith('.ppt') || lowerFileName.endsWith('.pptx')) {
        return '查看PPT预览';
    } else if (lowerFileName.endsWith('.jpg') || lowerFileName.endsWith('.jpeg') ||
        lowerFileName.endsWith('.png') || lowerFileName.endsWith('.gif')) {
        return '查看图片预览';
    } else if (lowerFileName.endsWith('.txt')) {
        return '查看文本预览';
    } else {
        return '查看文件预览';
    }
};

// 获取文件预览按钮图标
const getPreviewButtonIcon = (fileName) => {
    if (!fileName) return 'mdi-text-box-outline';

    const lowerFileName = fileName.toLowerCase();

    if (lowerFileName.endsWith('.pdf')) {
        return 'mdi-file-pdf-box';
    } else if (lowerFileName.endsWith('.doc') || lowerFileName.endsWith('.docx')) {
        return 'mdi-file-word';
    } else if (lowerFileName.endsWith('.xls') || lowerFileName.endsWith('.xlsx')) {
        return 'mdi-file-excel';
    } else if (lowerFileName.endsWith('.ppt') || lowerFileName.endsWith('.pptx')) {
        return 'mdi-file-powerpoint';
    } else if (lowerFileName.endsWith('.jpg') || lowerFileName.endsWith('.jpeg') ||
        lowerFileName.endsWith('.png') || lowerFileName.endsWith('.gif')) {
        return 'mdi-file-image';
    } else if (lowerFileName.endsWith('.txt')) {
        return 'mdi-file-document-outline';
    } else {
        return 'mdi-text-box-outline';
    }
};

// 添加获取文件位置的函数
const getFileLocation = (file) => {
    if (!file || !file.repoId) return getCurrentKnowledgeBaseName();

    // 根据文件的repoId查找对应的知识库
    const repo = repoList.value.find(r => r.repoId == file.repoId);
    if (repo) {
        return repo.repoDesc || '未命名知识库';
    }

    // 如果找不到对应的知识库，返回当前知识库名称
    return getCurrentKnowledgeBaseName();
};

// 添加计算属性，动态显示当前知识库路径
const currentKnowledgeBasePath = computed(() => {
    // 根据当前选中的菜单项返回对应的知识库路径
    if (activeMenuGroup.value == 'public') {
        return '公共知识库广场';
    } else if (activeMenuGroup.value == 'unit') {
        return `单位知识库/${getCurrentKnowledgeBaseName()}`;
    } else {
        return '知识库';
    }
});

// 添加计算总文件大小的计算属性
const totalFileSize = computed(() => {
    // 如果没有文件，返回0
    if (fileList.value.length === 0) return 0;

    // 累加所有文件大小
    const totalSize = fileList.value.reduce((sum, file) => {
        // 确保fileSize存在且为数字
        const fileSize = file.fileSize ? Number(file.fileSize) : 0;
        return sum + fileSize;
    }, 0);

    return totalSize;
});

// 格式化后的总文件大小
const formattedTotalFileSize = computed(() => {
    return formatFileSize(totalFileSize.value);
});

// 添加计算属性，判断是否思考时间过长
const isThinkingTooLong = (msg) => {
    const thinkingDuration = Date.now() - (msg.thinkingStartTime || 0);
    return thinkingDuration > 5000;
};

// 确保思考步骤始终可见
const ensureThinkingStepsVisible = () => {
    // 立即设置思考可见性变量为true
    isThinkingVisible.value = true;

    // 处理所有系统消息（不仅仅是最后一条）
    const systemMessages = messages.value.filter(msg =>
        msg.type === 'system' && !msg.welcome && !msg.thinking
    );

    console.log('检查思考步骤可见性，系统消息数量:', systemMessages.length);

    // 检查所有系统消息
    systemMessages.forEach(systemMessage => {
        // 记录状态
        console.log('系统消息是否有思考步骤:',
            systemMessage.thinkingSteps ? '是，数量=' + systemMessage.thinkingSteps.length : '否');

        // 如果已有思考步骤，确保保留
        if (systemMessage.thinkingSteps && systemMessage.thinkingSteps.length > 0) {
            systemMessage.showThinkingSteps = true;
            systemMessage.preserveThinking = true; // 添加保留思考标志
            console.log('保留现有思考步骤，数量:', systemMessage.thinkingSteps.length);
        }
        // 如果没有思考步骤，尝试从内容中提取
        else {
            try {
                // 首先检查原始内容中的<think>标签
                const originalContent = systemMessage.originalContent || '';
                const content = systemMessage.content || '';
                const thinkingBlocks = [];
                let stepCount = 1;

                // 先从原始内容中提取<think>标签
                const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
                let match;

                while ((match = thinkRegex.exec(originalContent)) !== null) {
                    // 获取思考内容
                    let thinkContent = match[1];

                    // 使用processThinkingContent函数处理内容，确保是纯文本
                    thinkContent = processThinkingContent(thinkContent);

                    // 如果内容超过1000字符，截断并添加提示
                    if (thinkContent.length > 1000) {
                        thinkContent = thinkContent.substring(0, 1000) +
                            '... (内容已截断，显示前1000字符)';
                    }

                    thinkingBlocks.push({
                        step: stepCount++,
                        content: thinkContent,
                        isLast: false
                    });
                }

                // 如果<think>标签中没有内容，再尝试从thinking-block中提取
                if (thinkingBlocks.length === 0) {
                    const thinkingBlockRegex = /<div class="thinking-block[^"]*"[^>]*>([\s\S]*?)<\/div>/g;

                    while ((match = thinkingBlockRegex.exec(content)) !== null) {
                        // 获取思考内容
                        let thinkContent = match[1];

                        // 如果内容超过1000字符，截断并添加提示
                        if (thinkContent.length > 1000) {
                            thinkContent = thinkContent.substring(0, 1000) +
                                '<div style="font-style:italic; color:#666; margin-top:8px;">... (内容已截断，显示前1000字符)</div>';
                        }

                        thinkingBlocks.push({
                            step: stepCount++,
                            content: thinkContent,
                            isLast: false
                        });
                    }
                }

                // 如果找到了思考块，添加到消息对象中
                if (thinkingBlocks.length > 0) {
                    console.log('从内容中提取到思考块:', thinkingBlocks.length);
                    thinkingBlocks[thinkingBlocks.length - 1].isLast = true; // 将最后一个标记为最终结果
                    // 使用Vue的响应式API更新对象属性
                    systemMessage.thinkingSteps = thinkingBlocks;
                    systemMessage.showThinkingSteps = true;
                    systemMessage.preserveThinking = true;
                }
                // 不再使用正文内容作为思考内容，避免重复
                // 如果没有思考块，就不显示思考部分
                else if (content && !systemMessage.preserveThinking && false) {
                    // 注意：此分支已禁用，不再执行
                    console.log('不再使用正文内容作为思考内容');
                }
            } catch (e) {
                console.error('提取思考步骤失败:', e);
            }
        }
    });

    // 重新扫描所有消息，确保所有消息的思考步骤都可见
    messages.value.forEach(msg => {
        if (msg.thinkingSteps && msg.thinkingSteps.length > 0) {
            msg.showThinkingSteps = true;
            msg.preserveThinking = true; // 添加保留思考标志
        }
    });

    // 多次延迟执行，确保DOM完全更新后思考步骤可见
    const delays = [0, 100, 500, 1000, 2000, 3000]; // 合理的延迟范围

    // 函数：强制设置思考块样式为可见
    const forceVisibleStyle = (element) => {
        if (!element) return;

        // 设置重要样式，确保不会被其他样式覆盖
        element.style.setProperty('display', 'block', 'important');
        element.style.setProperty('visibility', 'visible', 'important');
        element.style.setProperty('opacity', '1', 'important');

        // 修改高度控制，设置最大高度并允许滚动
        if (element.classList.contains('thinking-steps')) {
            element.style.setProperty('max-height', '300px', 'important');
            element.style.setProperty('overflow-y', 'auto', 'important');
        } else {
            element.style.setProperty('height', 'auto', 'important');
            element.style.setProperty('max-height', '300px', 'important');
            element.style.setProperty('overflow-y', 'auto', 'important');
        }

        element.style.setProperty('position', 'static', 'important');
        element.style.setProperty('z-index', '10', 'important');
        element.style.setProperty('pointer-events', 'auto', 'important');
    };

    // 在不同的延迟后应用样式
    delays.forEach(delay => {
        setTimeout(() => {
            try {
                // 查找所有思考块相关元素
                const thinkingStepsContainers = document.querySelectorAll('.thinking-steps-container');
                const thinkingSteps = document.querySelectorAll('.thinking-steps');
                const thinkingContentBlocks = document.querySelectorAll('.thinking-content-block');
                const thinkingAllContent = document.querySelectorAll('.thinking-all-content');

                console.log(`延时${delay}ms:`, {
                    '思考步骤容器': thinkingStepsContainers.length,
                    '思考步骤': thinkingSteps.length,
                    '思考内容块': thinkingContentBlocks.length,
                    '思考全部内容': thinkingAllContent.length
                });

                // 为所有相关元素应用样式
                thinkingStepsContainers.forEach(forceVisibleStyle);
                thinkingSteps.forEach(el => {
                    if (!el.classList.contains('always-visible')) {
                        el.classList.add('always-visible');
                    }
                    forceVisibleStyle(el);
                });
                thinkingContentBlocks.forEach(forceVisibleStyle);
                thinkingAllContent.forEach(forceVisibleStyle);

                // 直接处理思考块元素
                const thinkingBlocks = document.querySelectorAll('.thinking-block');
                thinkingBlocks.forEach(block => {
                    block.style.setProperty('display', 'block', 'important');
                    block.style.setProperty('visibility', 'visible', 'important');
                    block.style.setProperty('opacity', '1', 'important');
                });
            } catch (e) {
                console.error(`延时${delay}ms应用样式失败:`, e);
            }
        }, delay);
    });
};

// 处理思考内容的函数，提取纯文本内容（简化版本）
const processThinkingContent = (content) => {
    if (!content) return '';

    try {
        // 检查内容是否是字符串类型，如果不是则转换为字符串
        if (typeof content !== 'string') {
            content = String(content);
        }

        console.log('处理思考内容:', content.substring(0, 100));

        // 优化处理：减少<br>标签的使用，避免行间距过大
        // 将转义的换行符转换为真实换行符，然后转换为空格
        return content.replace(/\\n/g, '\n').replace(/\n/g, ' ');
    } catch (e) {
        console.error('处理思考内容失败:', e);
        return String(content); // 确保返回字符串
    }
};

// 获取文件扩展名的函数
const getFileExtension = (language) => {
    // 根据语言类型返回对应的文件扩展名
    const extensionMap = {
        'javascript': '.js',
        'js': '.js',
        'typescript': '.ts',
        'ts': '.ts',
        'html': '.html',
        'css': '.css',
        'python': '.py',
        'py': '.py',
        'java': '.java',
        'c': '.c',
        'cpp': '.cpp',
        'csharp': '.cs',
        'cs': '.cs',
        'php': '.php',
        'ruby': '.rb',
        'go': '.go',
        'rust': '.rs',
        'json': '.json',
        'xml': '.xml',
        'yaml': '.yaml',
        'yml': '.yml',
        'markdown': '.md',
        'md': '.md',
        'sql': '.sql',
        'bash': '.sh',
        'shell': '.sh',
        'powershell': '.ps1',
        'dockerfile': '.dockerfile',
        'plaintext': '.txt'
    };

    return extensionMap[language.toLowerCase()] || '.txt';
};

// 检测是否为Markdown格式的函数（针对内容创作模式优化）
const isMarkdownContent = (text) => {
    if (!text) return false;

    // 强特征：明确的Markdown语法
    const strongMarkdownPatterns = [
        /^#{1,6}\s+.+$/m,        // 标题必须有内容 # 标题
        /```[\w]*\n[\s\S]*?\n```/,  // 完整的代码块
        /^\s*>\s+.+$/m,          // 引用必须有内容
        /\[.+\]\(https?:\/\/.+\)/,  // 完整的链接
        /!\[.*\]\(.+\)/,         // 图片链接
        /^\s*\|.+\|.+\|/m,       // 表格（至少两列）
        /^---+$/m,               // 分隔线
    ];

    // 弱特征：可能的Markdown语法
    const weakMarkdownPatterns = [
        /\*\*[^*]+\*\*/,         // 粗体（内容不能为空）
        /\*[^*]+\*/,             // 斜体（内容不能为空）
        /`[^`]+`/,               // 行内代码（内容不能为空）
        /^\s*[-*+]\s+.+$/m,      // 无序列表
        /^\s*\d+\.\s+.+$/m,      // 有序列表
    ];

    // 计算匹配的特征数量
    const strongMatches = strongMarkdownPatterns.filter(pattern => pattern.test(text)).length;
    const weakMatches = weakMarkdownPatterns.filter(pattern => pattern.test(text)).length;

    // 对于内容创作模式，更积极地检测markdown
    // 1个强特征 或 2个弱特征就认为是markdown
    const isMarkdown = strongMatches >= 1 || weakMatches >= 2;

    console.log('Markdown检测结果:', {
        textLength: text.length,
        textPreview: text.substring(0, 100),
        strongMatches,
        weakMatches,
        isMarkdown
    });

    return isMarkdown;
};

// 处理内容的主函数，支持Markdown和普通文本
const processContent = (text) => {
    if (!text) return '';

    console.log('processContent开始处理内容:', {
        textLength: text.length,
        textPreview: text.substring(0, 200),
        currentMode: selectedMode.value?.value
    });

    // 检测是否为Markdown格式
    const isMarkdown = isMarkdownContent(text);

    console.log('processContent检测结果:', {
        isMarkdown,
        willUseMarkdownProcessor: isMarkdown
    });

    // 如果是Markdown格式，使用marked库解析
    if (isMarkdown) {
        const result = processMarkdownContent(text);
        console.log('processContent使用Markdown处理器完成');
        return result;
    } else {
        // 否则使用原有的简单处理逻辑
        const result = processSimpleContent(text);
        console.log('processContent使用简单处理器完成');
        return result;
    }
};

// 使用marked库处理Markdown内容
const processMarkdownContent = (text) => {
    console.log('processMarkdownContent开始处理:', {
        textLength: text.length,
        textPreview: text.substring(0, 100)
    });

    let processedText = text;

    // 首先处理思考块，保持原有逻辑
    processedText = processThinkingBlocks(processedText);
    console.log('processMarkdownContent思考块处理完成');

    try {
        // 配置marked选项
        if (typeof marked !== 'undefined') {
            console.log('marked库可用，开始配置和解析');

            marked.setOptions({
                highlight: function (code, lang) {
                    if (lang && hljs.getLanguage(lang)) {
                        try {
                            return hljs.highlight(code, { language: lang }).value;
                        } catch (err) {
                            console.error('代码高亮失败:', err);
                        }
                    }
                    return hljs.highlightAuto(code).value;
                },
                langPrefix: 'hljs language-',
                breaks: false, // 修复：不自动将换行符转换为<br>，避免行间距过大
                gfm: true,
                tables: true,
                sanitize: false,
                smartLists: true,
                smartypants: false // 修复：关闭智能标点，避免引号等字符被错误转换
            });

            // 使用marked解析Markdown
            processedText = marked.parse(processedText);
            console.log('marked解析完成，结果长度:', processedText.length);

            // 优化超链接：让所有链接在新标签页中打开
            processedText = processedText.replace(/<a\s+href="([^"]*)"([^>]*)>/g, (match, href, attrs) => {
                // 检查是否已经有target属性
                if (!attrs.includes('target=')) {
                    // 添加target="_blank"和rel="noopener noreferrer"以提高安全性
                    return `<a href="${href}" target="_blank" rel="noopener noreferrer"${attrs}>`;
                }
                return match; // 如果已经有target属性，保持原样
            });
            console.log('超链接优化完成：所有链接将在新标签页中打开');

            // 添加样式类和最大宽度控制
            processedText = `<div class="markdown-content" style="max-width: 100%; word-wrap: break-word; overflow-wrap: break-word;">${processedText}</div>`;

        } else {
            console.warn('marked库未加载，使用简单处理');
            processedText = processSimpleContent(text);
        }
    } catch (error) {
        console.error('Markdown解析失败，使用简单处理:', error);
        processedText = processSimpleContent(text);
    }

    console.log('processMarkdownContent处理完成');
    return processedText;
};

// 处理思考块的函数（简化版本）
const processThinkingBlocks = (text) => {
    let processedText = text;

    // 只处理明确的思考块标签，避免误判
    const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
    const thinkingRegex = /<thinking>([\s\S]*?)<\/thinking>/g;
    const thinkingBracketRegex = /\[THINKING\]([\s\S]*?)\[\/THINKING\]/g;

    // 处理 <think> 标签
    if (processedText.includes('<think>')) {
        processedText = processedText.replace(thinkRegex, (match, thinkContent) => {
            const cleanedContent = processThinkingContent(thinkContent);
            return `<div style="background-color: #f5f5f5; color: #888; padding: 12px 16px; margin: 8px 0; border-radius: 6px; font-style: italic; border-left: 3px solid #ddd;">
        ${cleanedContent}
      </div>`;
        });
    }

    // 处理 <thinking> 标签
    if (processedText.includes('<thinking>')) {
        processedText = processedText.replace(thinkingRegex, (match, thinkContent) => {
            const cleanedContent = processThinkingContent(thinkContent);
            return `<div style="background-color: #f5f5f5; color: #888; padding: 12px 16px; margin: 8px 0; border-radius: 6px; font-style: italic; border-left: 3px solid #ddd;">
        ${cleanedContent}
      </div>`;
        });
    }

    // 处理 [THINKING] 标签
    if (processedText.includes('[THINKING]')) {
        processedText = processedText.replace(thinkingBracketRegex, (match, thinkContent) => {
            const cleanedContent = processThinkingContent(thinkContent);
            return `<div style="background-color: #f5f5f5; color: #888; padding: 12px 16px; margin: 8px 0; border-radius: 6px; font-style: italic; border-left: 3px solid #ddd;">
        ${cleanedContent}
      </div>`;
        });
    }

    // 处理未闭合的思考块
    if (processedText.includes('<think>') && !processedText.includes('</think>')) {
        processedText = processedText.replace(/<think>([^]*)$/, (match, thinkContent) => {
            const cleanedContent = processThinkingContent(thinkContent || '');
            return `<div style="background-color: #f5f5f5; color: #888; padding: 12px 16px; margin: 8px 0; border-radius: 6px; font-style: italic; border-left: 3px solid #ddd;">
        ${cleanedContent || '思考中...'}
      </div>`;
        });
    }

    return processedText;
};

// 处理简单文本内容（原有逻辑）
const processSimpleContent = (text) => {
    // 首先处理思考块
    let processedText = processThinkingBlocks(text);

    // 然后处理代码块，使用更可靠的正则表达式
    const codeBlockRegex = /```([\w-]*)\s*([\s\S]*?)```/g;

    // 收集所有代码块替换信息，以便后续一次性替换
    const replacements = [];
    let match;

    while ((match = codeBlockRegex.exec(text)) !== null) {
        const fullMatch = match[0];
        const language = match[1] || 'plaintext';
        const code = match[2].trim();
        const codeBlockId = `code-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        const fileExtension = getFileExtension(language);
        const downloadFilename = `code-snippet${fileExtension}`;

        // 判断是否可以运行的语言
        const runnableLanguages = ['javascript', 'js', 'python', 'py', 'html', 'css'];
        const isRunnable = runnableLanguages.includes(language.toLowerCase());

        // 使用highlight.js进行代码高亮
        let highlightedCode;

        // 规范化语言标识符
        let langName = language ? language.toLowerCase() : '';
        if (langName === 'py') langName = 'python';
        if (langName === 'js') langName = 'javascript';
        if (langName === 'ts') langName = 'typescript';

        try {
            // 先对代码进行HTML转义，防止HTML标签被错误解析
            const escapedCode = code
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;');

            // 使用highlight.js进行代码高亮
            if (langName) {
                highlightedCode = hljs.highlight(escapedCode, { language: langName }).value;
            } else {
                // 如果没有指定语言，使用自动检测
                highlightedCode = hljs.highlightAuto(escapedCode).value;
            }
        } catch (e) {
            console.error('代码高亮失败:', e);
            // 高亮失败时，使用基本的HTML转义
            highlightedCode = code
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;');
        }

        // 处理代码，按行分割以便每行显示
        const codeLines = highlightedCode.split('\n');

        // 创建代码块HTML，使用Atom One Dark风格
        let codeBlockHtml = '';

        // 特别处理SQL语句，强制换行显示
        if (langName === 'sql') {
            codeBlockHtml = `
      <div class="code-block">
        <div class="code-header">
          <div class="code-header-left">
            <span class="code-language">${language ? language.toLowerCase() : 'code'}</span>
          </div>
          <div class="code-actions">
          </div>
        </div>
        <div class="code-pre line-numbers-mode sql-code">
          <pre><code id="${codeBlockId}" class="language-${language}" data-language="${language}">
${codeLines.map((line, index) => `<div class="line-number">${index + 1}</div><div class="line-content" style="display: block; white-space: pre-wrap;">${line || ' '}</div>`).join('\n')}
          </code></pre>
        </div>
      </div>
    `;
        } else {
            // 其他语言使用标准显示
            codeBlockHtml = `
      <div class="code-block">
        <div class="code-header">
          <div class="code-header-left">
            <span class="code-language">${language ? language.toLowerCase() : 'code'}</span>
          </div>
          <div class="code-actions">
            ${isRunnable ? `
            <button class="action-button run-button" onclick="runCode('${codeBlockId}', '${language}')">
              <i class="mdi mdi-play"></i> 运行
            </button>` : ''}
          </div>
        </div>
        <div class="code-pre line-numbers-mode">
          <pre><code id="${codeBlockId}" class="language-${language}" data-language="${language}">
${codeLines.map((line, index) => `<div class="line-number">${index + 1}</div><div class="line-content">${line || ' '}</div>`).join('\n')}
          </code></pre>
        </div>
      </div>
    `;
        }

        replacements.push({
            original: fullMatch,
            replacement: codeBlockHtml
        });
    }

    // 执行替换
    replacements.forEach(({ original, replacement }) => {
        processedText = processedText.replace(original, replacement);
    });

    // 添加复制代码功能
    // 复制和下载功能已移除

    // 添加运行代码功能
    if (!window.runCode) {
        window.runCode = function (codeId, language) {
            const codeElement = document.getElementById(codeId);
            if (!codeElement) return;

            // 获取代码文本
            const codeText = codeElement.textContent;

            // 根据语言类型执行不同的运行逻辑
            if (language === 'javascript' || language === 'js') {
                try {
                    // 创建一个新的Function来执行代码
                    const result = new Function(codeText)();
                    console.log('运行结果:', result);

                    // 显示运行成功
                    const button = codeElement.closest('.code-block').querySelector('.run-button');
                    const originalHTML = button.innerHTML;
                    button.innerHTML = `<i class="mdi mdi-check"></i> 已运行`;

                    // 2秒后恢复原样
                    setTimeout(() => {
                        button.innerHTML = originalHTML;
                    }, 2000);

                } catch (err) {
                    console.error('运行JavaScript代码时出错:', err);
                    alert('运行代码时出错: ' + err.message);
                }
            } else if (language == 'html') {
                // 创建一个新窗口来显示HTML
                const newWindow = window.open('', '_blank');
                newWindow.document.write(codeText);
                newWindow.document.close();

                // 显示运行成功
                const button = codeElement.closest('.code-block').querySelector('.run-button');
                const originalHTML = button.innerHTML;
                button.innerHTML = `<i class="mdi mdi-check"></i> 已运行`;

                // 2秒后恢复原样
                setTimeout(() => {
                    button.innerHTML = originalHTML;
                }, 2000);
            } else {
                alert(`暂不支持运行 ${language} 代码`);
            }
        };
    }

    // 处理基本的Markdown元素（简单版本）
    // 注意处理顺序：先处理块级元素，再处理内联元素

    // 处理标题
    processedText = processedText.replace(/^### (.*?)$/gm, '<h3>$1</h3>');
    processedText = processedText.replace(/^## (.*?)$/gm, '<h2>$1</h2>');
    processedText = processedText.replace(/^# (.*?)$/gm, '<h1>$1</h1>');

    // 处理列表
    // 处理有序列表（数字开头的列表项）
    let listStarted = false;
    let listItems = [];

    // 将文本按行分割处理
    const lines = processedText.split('\n');
    const resultLines = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const numberedListMatch = line.match(/^(\d+)\.\s+(.*?)$/);

        if (numberedListMatch) {
            // 这是一个有序列表项
            if (!listStarted) {
                // 开始新的列表
                listStarted = true;
                listItems = [];
            }

            // 提取序号和内容
            const number = parseInt(numberedListMatch[1], 10);
            const content = numberedListMatch[2].trim();

            // 添加到列表项
            listItems.push({ number, content });
        } else if (listStarted && (line.trim() === '' || i === lines.length - 1)) {
            // 列表结束（遇到空行或文本结束）
            if (listItems.length > 0) {
                // 构建有序列表HTML
                let listHtml = '<ol class="custom-numbered-list" style="margin-left: 20px;">';
                listItems.forEach(item => {
                    listHtml += `<li value="${item.number}">${item.content}</li>`;
                });
                listHtml += '</ol>';

                resultLines.push(listHtml);
            }

            listStarted = false;

            // 如果是空行，添加它
            if (line.trim() === '') {
                resultLines.push(line);
            } else {
                // 如果是最后一行且不是列表项，添加它
                resultLines.push(line);
            }
        } else if (listStarted) {
            // 这是列表中的非列表项（可能是列表项的延续）
            // 将其附加到最后一个列表项
            if (listItems.length > 0) {
                listItems[listItems.length - 1].content += ' ' + line.trim();
            }
        } else {
            // 常规行，不是列表的一部分
            resultLines.push(line);
        }
    }

    // 如果列表在文本末尾没有结束，手动结束它
    if (listStarted && listItems.length > 0) {
        let listHtml = '<ol class="custom-numbered-list" style="margin-left: 20px;">';
        listItems.forEach(item => {
            listHtml += `<li value="${item.number}">${item.content}</li>`;
        });
        listHtml += '</ol>';

        resultLines.push(listHtml);
    }

    // 重新组合处理后的文本
    processedText = resultLines.join('\n');

    // 处理无序列表（* 或 - 开头的列表项）
    processedText = processedText.replace(/(?:^[*-] .+\n?)+/gm, (match) => {
        const items = match.split('\n')
            .filter(line => line.trim())
            .map(line => {
                const content = line.replace(/^[*-]\s+/, '').trim();
                return `<li>${content}</li>`;
            })
            .join('');

        return `<ul class="custom-bullet-list" style="margin-left: 20px;">${items}</ul>`;
    });

    // 处理引用
    processedText = processedText.replace(/^> (.*?)$/gm, '<blockquote>$1</blockquote>');

    // 处理内联元素
    // 粗体
    processedText = processedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    processedText = processedText.replace(/__(.*?)__/g, '<strong>$1</strong>');

    // 斜体
    processedText = processedText.replace(/\*(.*?)\*/g, '<em>$1</em>');
    processedText = processedText.replace(/_(.*?)_/g, '<em>$1</em>');

    // 代码段
    processedText = processedText.replace(/`([^`]+)`/g, '<code>$1</code>');

    // 链接 - 优化：在新标签页打开并添加安全属性
    processedText = processedText.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

    // 图片
    processedText = processedText.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1">');

    // 优化换行处理，减少不必要的<br>标签
    // 把连续的换行（段落分隔）替换为p标签
    processedText = processedText.replace(/\n\s*\n/g, '</p><p>');

    // 对于单个换行，转换为空格而不是<br>，避免行间距过大
    // 只有在代码块外的单个换行才转换为空格
    processedText = processedText.replace(/\n/g, ' ');

    // 确保内容在p标签内
    if (!processedText.startsWith('<')) {
        processedText = `<p>${processedText}</p>`;
    }

    // 在处理完内容后，确保在下一个渲染周期应用代码高亮
    nextTick(() => {
        applyHighlight();
    });

    console.log('简单处理后的文本长度:', processedText.length);

    // 如果处理后的内容为空，返回原始内容
    if (!processedText || processedText === '<p></p>') {
        console.log('处理后内容为空，返回原始文本');
        return `<p>${text}</p>`;
    }

    // 添加最大宽度和自动换行样式，控制内容不超出布局
    processedText = `<div style="max-width: 100%; word-wrap: break-word; overflow-wrap: break-word;">${processedText}</div>`;

    return processedText;
};

// 创建一个强制滚动函数
const forceScroll = () => {
    // 直接滚动页面到底部
    window.scrollTo(0, document.body.scrollHeight);

    // 如果聊天容器存在，滚动容器
    if (chatContainer.value) {
        chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
    }
};

// 重新处理文件（重新解析）
const reprocessFile = async (file) => {
    if (!file.fileId) {
        showToast('无法处理文件，文件ID不存在', 'error');
        return;
    }

    try {
        // 调用chunksDoc接口重新处理文件
        const chunksData = {
            ids: [file.fileId],
            datasetId: file.fileMd5
        };

        console.log('调用chunksDoc接口重新处理文件，参数:', chunksData);
        const chunksResponse = await chunksDoc(chunksData);
        console.log('chunksDoc接口响应:', chunksResponse);

        // 检查chunksDoc接口是否成功
        if (chunksResponse && chunksResponse.code === 0) {
            showToast('文件重新解析已开始，请稍后查看结果');

            // 刷新文件列表，不修改本地状态，直接使用接口返回的排序
            setTimeout(async () => {
                await loadKnowledgeBaseFiles(repoIds.value);
            }, 300);
        } else {
            showToast('解析失败', 'error');
            throw new Error('文件处理失败');
        }
    } catch (error) {
        console.error('重新处理文件失败:', error);
        showToast('文件重新解析失败，请重试', 'error');
    }
};

// 向量解析文件（处理UNPARSED状态的文件）
const parseVectorFile = async (file) => {
    if (!file.fileId) {
        showToast('无法处理文件，文件ID不存在', 'error');
        return;
    }

    try {
        // 调用chunksDoc接口处理文件
        const chunksData = {
            ids: [file.fileId],
            datasetId: file.fileMd5
        };

        console.log('调用chunksDoc接口进行向量解析，参数:', chunksData);
        const chunksResponse = await chunksDoc(chunksData);
        console.log('chunksDoc接口响应:', chunksResponse);

        // 检查chunksDoc接口是否成功
        if (chunksResponse && chunksResponse.code === 0) {
            showToast('向量解析已开始，请稍后查看结果');

            // 刷新文件列表，不修改本地状态，直接使用接口返回的排序
            setTimeout(async () => {
                await loadKnowledgeBaseFiles(repoIds.value);
            }, 300);
        } else {
            showToast('向量解析失败', 'error');
            throw new Error('文件向量解析失败');
        }
    } catch (error) {
        console.error('向量解析文件失败:', error);
        showToast('向量解析失败，请重试', 'error');
    }
};

// 预览文档方法
const previewDocument = (doc) => {
    console.log('预览文档:', doc);
    if (!doc || !doc.doc_id) {
        showToast('文档ID不存在，无法预览', 'error');
        return;
    }

    // 直接调用viewFileInline方法预览文档
    viewFileInline(doc);
};

// 在组件挂载后初始化highlight.js
nextTick(() => {
    applyHighlight();
    scrollToBottom();

    // 创建MutationObserver监视聊天容器内容变化
    if (chatContainer.value) {
        const observer = new MutationObserver(() => {
            // 当DOM变化时，立即滚动一次
            forceScroll();

            // 然后在不同时间点再次滚动，确保捕获所有内容更新
            [100, 300, 500, 1000].forEach(delay => {
                setTimeout(forceScroll, delay);
            });
        });

        // 配置观察选项
        const config = {
            childList: true,     // 观察子节点的添加或删除
            subtree: true,       // 观察所有后代节点
            characterData: true, // 观察文本内容的变化
            attributes: true     // 观察属性变化
        };

        // 开始观察
        observer.observe(chatContainer.value, config);

        // 添加全局滚动监听器
        // window.addEventListener('resize', forceScroll);

        // 每隔一段时间检查一次是否需要滚动（处理异步加载的内容）
        // const scrollInterval = setInterval(forceScroll, 1000);

        // 组件卸载时清除定时器和监听器
        onUnmounted(() => {
            observer.disconnect();
            // window.removeEventListener('resize', forceScroll);
            // clearInterval(scrollInterval);
        });
    }

    // 添加一个特定的滚动触发器，监听消息容器的滚动事件
    document.addEventListener('scroll', () => {
        // 检查是否接近底部（距离底部100px以内）
        const isNearBottom =
            window.innerHeight + window.scrollY >=
            document.body.offsetHeight - 100;

        if (isNearBottom && messages.value.length > 0) {
            // 如果接近底部且有消息，强制滚动到底部
            forceScroll();
        }
    });
});

// 替换原来的onUnmounted函数，包含所有卸载时需要执行的清理操作
onUnmounted(() => {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', checkDeviceAndOrientation);

    // 移除滚动事件监听
    window.removeEventListener('scroll', handleScroll);

    // 清除所有定时器
    if (toast.value.timeout) {
        clearTimeout(toast.value.timeout);
    }

    // 停止文件状态轮询
    stopFileStatusPolling();

    // 释放Blob URL资源
    if (fileViewerUrl.value && fileViewerUrl.value.startsWith('blob:')) {
        URL.revokeObjectURL(fileViewerUrl.value);
    }

    // 释放fileInfo中的Blob URL资源
    if (fileInfo.value && fileInfo.value.filePath && typeof fileInfo.value.filePath === 'string' && fileInfo.value.filePath.startsWith('blob:')) {
        URL.revokeObjectURL(fileInfo.value.filePath);
    }

    // 注意：其他onUnmounted清理操作已经移到onMounted函数内部的闭包中
});

// 监听消息变化，当有新消息时重新应用代码高亮
watch(() => messages.value, () => {
    nextTick(() => {
        applyHighlight();
    });
}, { deep: true });

// 应用代码高亮的函数
const applyHighlight = () => {
    document.querySelectorAll('pre code').forEach((block) => {
        if (!block.classList.contains('hljs')) {
            hljs.highlightElement(block);
        }
    });

    // 确保代码块中的代码正确显示
    document.querySelectorAll('.code-pre').forEach((pre) => {
        pre.style.overflowX = 'auto';

        // 检查是否是行号模式
        const isLineNumbersMode = pre.parentElement.classList.contains('line-numbers-mode');

        if (!isLineNumbersMode) {
            pre.style.whiteSpace = 'pre';

            const code = pre.querySelector('code');
            if (code) {
                code.style.whiteSpace = 'pre';
                code.style.display = 'block';
            }
        } else {
            // 行号模式的样式已经在CSS中定义
            const lineContents = pre.querySelectorAll('.line-content');
            if (lineContents.length > 0) {
                // 确保每行的内容可以适当换行
                lineContents.forEach(lineContent => {
                    lineContent.style.whiteSpace = 'pre-wrap';
                });
            }
        }
    });
};

// 修改分页处理
const handleSizeChange = (val) => {
    pageSize.value = val;
    currentPage.value = 1;
};

const handleCurrentChange = (val) => {
    currentPage.value = val;
};

// 用户相关函数
const fetchUsers = async () => {
    loading.value = true;
    try {
        const params = {
            pageNum: currentUserPage.value,
            pageSize: userPageSize.value,
            nickName: userSearchQuery.value?.name || '',
            phoneNumber: userSearchQuery.value?.phone || '',
            status: '',
        };

        const res = await userListApi(params);
        if (res && res.data) {
            userSelectOptions.value = res.data.rows.map(user => ({ ...user, selected: false }));
            totalUsers.value = res.data.total || 0;
            userSearchResult.value = true;
        }
    } catch (error) {
        console.error('获取用户列表失败:', error);
    } finally {
        loading.value = false;
    }
};

// 监听搜索和筛选条件变化
watch([memberSearchQuery, memberStatusFilter], () => {
    currentPage.value = 1; // 重置为第一页
    fetchMembersList();
}, { deep: true });

// 处理角色变更
const handleRoleChange = async (member, permissionType) => {
    console.log(member);

    if (!currentRepoId.value) {
        showToast('未找到当前知识库ID，无法修改角色', 'error');
        return;
    }

    try {
        // 构建请求参数
        const params = {
            id: member.id,
            // repoId: currentRepoId.value,
            operationPermission: permissionType == 1 ? 'EDIT' : 'READ' // 1:管理员(EDIT) 2:成员(READ)
        };

        // 显示加载状态
        isLoading.value = true;

        // 调用MembersEdit接口修改成员权限
        const res = await MembersEdit(params);

        if (res.code == 0) {
            // 更新本地数据的operationPermission值
            member.operationPermission = permissionType == 1 ? 'EDIT' : 'READ';
            showToast('角色修改成功', 'success');
            // 重新加载成员列表
            await fetchMembersList();
        } else {
            showToast(res.msg || '角色修改失败', 'error');
        }
    } catch (error) {
        console.error('修改角色失败:', error);
        showToast('修改角色失败', 'error');
    } finally {
        isLoading.value = false;
    }
};

// 处理成员菜单命令
const handleMemberCommand = async (command) => {
    const { type, member } = command;

    if (!member || !member.userId || !currentRepoId.value) {
        showToast('操作失败：缺少必要参数', 'error');
        return;
    }

    try {
        // 显示加载状态
        isLoading.value = true;

        // 根据命令类型设置权限类型
        // EDIT - 管理员(1), READ - 成员(2)
        const permissionType = type === 'EDIT' ? 1 : 2;

        // 构建请求参数
        const params = {
            userId: member.userId,
            repoId: currentRepoId.value,
            permissionType: permissionType
        };

        // 调用添加成员API (添加会覆盖原有权限)
        const res = await MembersAdd(params);

        if (res.code === 0) {
            showToast('权限修改成功', 'success');
            // 重新加载成员列表
            await fetchMembersList();
        } else {
            showToast(res.msg || '权限修改失败', 'error');
        }
    } catch (error) {
        console.error('修改权限失败:', error);
        showToast('修改权限失败', 'error');
    } finally {
        isLoading.value = false;
    }
};

// 编辑成员
const editMember = (member) => {
    // 实现成员编辑功能
    console.log('编辑成员:', member);
    showToast('编辑功能开发中', 'info');
};

// 删除成员
const deleteMember = async (member) => {
    if (!member || !member.id || !currentRepoId.value) {
        showToast('删除失败：缺少必要参数', 'error');
        return;
    }

    try {
        // 显示确认对话框
        proxy.$modal.confirm('确定要删除该成员吗?').then(async () => {
            isLoading.value = true;

            // 调用MembersRemove接口删除成员
            const res = await MembersRemove({
                ids: member.id // 使用成员记录id作为ids参数
            });

            if (res.code === 0) {
                showToast('成员删除成功', 'success');
                // 重新加载成员列表
                await fetchMembersList();
                // 重新加载左侧知识库列表
                await getLeftList();
            } else {
                showToast(res.msg || '删除失败', 'error');
            }
        }).catch(() => {
            // 用户取消删除操作
        }).finally(() => {
            isLoading.value = false;
        });
    } catch (error) {
        console.error('删除成员失败:', error);
        showToast('删除成员失败', 'error');
        isLoading.value = false;
    }
};

// 处理成员选择变化
const handleSelectionChange = (selection) => {
    selectedMembers.value = selection;

    // 记录选中的成员ID，并确保这些ID可以在控制台中查看
    if (selection.length > 0) {
        const selectedIds = selection.map(member => member.id);
        console.log('选中的成员数量:', selection.length);
        console.log('选中的成员ID:', selectedIds);

        // 如果需要将这些ID用于其他操作，可以在这里添加相关代码
    }
};

// 获取选中成员的ID列表
const getSelectedMemberIds = () => {
    return selectedMembers.value.map(member => member.id);
};

// 重试上传文件
const retryUpload = async (file) => {
    if (!file.originalFile) {
        // 如果没有原始文件信息，提示用户重新选择文件
        showToast('无法重试上传，请重新选择文件', 'error');
        // 打开上传对话框让用户重新上传
        openUploadDialog();
        return;
    }

    // 不显示全屏加载动画
    const loadingInstance = null;

    try {
        // 获取当前知识库ID
        let currentRepoIdValue = null;
        // 确定当前是否在单位知识库
        const isUnitKnowledgeBase = activeMenuGroup.value === 'unit';

        if (!isUnitKnowledgeBase) {
            // 如果不是单位知识库，获取当前知识库ID
            if (activeMenuItem.value && activeMenuItem.value.startsWith('repo_')) {
                currentRepoIdValue = activeMenuItem.value.split('_')[1];
            }
        }

        // 更新文件状态为上传中
        const index = fileList.value.findIndex(item => item.id === file.id);
        if (index !== -1) {
            fileList.value[index].status = 'UPLOADING';
            fileList.value[index].progress = 0;
        }

        // 设置一个小延迟，确保状态更新到DOM
        await new Promise(resolve => setTimeout(resolve, 100));

        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file.originalFile);

        // 获取当前知识库的remark字段作为datasetId
        let currentDatasetId = '';
        if (repoIds.value) {
            const currentRepo = repoList.value.find(r => r.repoId == repoIds.value);
            if (currentRepo && currentRepo.remark) {
                currentDatasetId = currentRepo.remark;
                console.log('重试上传使用知识库remark字段作为datasetId:', currentDatasetId);
            }
        }

        // 使用upFile接口上传文件，并添加进度回调1
        console.log('重试上传文件，知识库ID:', repoIds.value, '文件权限:', filePermissionType.value);
        const response = await upFile(formData, (progressEvent) => {
            if (progressEvent.lengthComputable) {
                // 更新上传进度 - 限制在0-70%范围内，留出30%给后续处理
                const actualPercent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                const displayPercent = Math.min(Math.round(actualPercent * 0.7), 70); // 最多显示到70%

                console.log('重试上传进度:', actualPercent + '%', '显示进度:', displayPercent + '%', '已上传:', progressEvent.loaded, '总大小:', progressEvent.total);

                const fileIndex = fileList.value.findIndex(item => item.id == file.id);
                if (fileIndex !== -1) {
                    // 确保进度从0开始逐渐增加
                    if (displayPercent > fileList.value[fileIndex].progress) {
                        fileList.value[fileIndex].progress = displayPercent;
                    }

                    // 添加：如果检测到进度卡住，创建自动递增定时器（适用于任何百分比）
                    if (!window['autoProgress_' + file.id]) {
                        console.log('检测到重试上传可能卡住，启动自动进度递增');
                        window['autoProgress_' + file.id] = setInterval(() => {
                            const currentIdx = fileList.value.findIndex(item => item.id === file.id);
                            if (currentIdx !== -1) {
                                const currentFile = fileList.value[currentIdx];
                                // 如果文件上传状态改变或者进度已经大于98%，清除定时器
                                if (currentFile.status !== 'UPLOADING' || currentFile.progress >= 98) {
                                    clearInterval(window['autoProgress_' + file.id]);
                                    window['autoProgress_' + file.id] = null;
                                    console.log('清除重试上传自动进度递增定时器');
                                } else {
                                    // 根据当前进度确定增加速度
                                    let increment = 0.8; // 默认增长率更快
                                    const currentProgress = currentFile.progress;

                                    // 进度越高，增加越慢
                                    if (currentProgress > 80) increment = 0.1; // 80%后显著减慢
                                    else if (currentProgress > 50) increment = 0.5; // 50-80%之间中等速度

                                    // 确保不会超过98%，为最终完成留出空间
                                    const newProgress = Math.min(currentProgress + increment, 98);
                                    fileList.value[currentIdx].progress = newProgress;
                                    console.log('自动增加重试上传进度到:', newProgress.toFixed(2) + '%');
                                }
                            } else {
                                // 如果找不到文件，也要清除定时器
                                clearInterval(window['autoProgress_' + file.id]);
                                window['autoProgress_' + file.id] = null;
                                console.log('找不到文件，清除重试上传自动进度递增定时器');
                            }
                        }, 800); // 较长的间隔，确保进度增长很慢很平滑
                    }
                }
            }
        }, repoIds.value, filePermissionType.value);
        console.log('文件信息', response, file.originalFile);

        // 打印最原始的响应数据
        console.log('重试上传原始响应数据:', JSON.stringify(response.data));
        console.log('重试上传完整响应对象:', response);

        // 检查upFile接口返回的code是否为0，如果不是则直接显示上传失败并返回
        if (response.code !== 0) {
            console.error('重试上传接口返回错误:', response.msg || '重试上传失败');

            // 从文件列表中移除失败的文件
            const failIndex = fileList.value.findIndex(item => item.id === file.id);
            if (failIndex !== -1) {
                // 直接从列表中删除失败的文件，而不是更新其状态
                fileList.value.splice(failIndex, 1);

                // 清除自动进度递增定时器
                if (window['autoProgress_' + file.id]) {
                    clearInterval(window['autoProgress_' + file.id]);
                    window['autoProgress_' + file.id] = null;
                }

                // 清除其他所有计时器
                clearInterval(window['progressInterval_' + file.id]);
                clearInterval(window['dotsAnimation_' + file.id]);
            }

            // 显示错误消息
            showToast(response.msg || '重试上传失败', 'error');
            return; // 直接返回，不执行后续代码
        }

        // 检查并提取需要的数据
        let responseData = {};
        if (response.data && response.data.data) {
            if (Array.isArray(response.data.data) && response.data.data.length > 0) {
                // 如果是数组，取第一个元素
                responseData = response.data.data[0];
                console.log('响应数据是数组，使用第一项:', responseData);
            } else {
                // 如果不是数组，直接使用
                responseData = response.data.data;
                console.log('响应数据是对象:', responseData);
            }
        }

        // 检查响应数据中的各个字段
        console.log('提取的响应数据字段:');
        console.log('- dataset_id:', responseData.dataset_id);
        console.log('- id:', responseData.id);
        console.log('- type:', responseData.type);
        console.log('- md5:', responseData.md5);
        console.log('- creator:', responseData.creator);
        console.log('- userId:', responseData.userId);

        // 获取文件扩展名
        const fileExt = file.originalFile.name.split('.').pop() || '';

        // 获取dataset_id - 这是文档ID
        const dataset_id = responseData.dataset_id || '';

        // 构建文件路径
        const filePath = `https://ragflow-4029862aab3ad76af3e3a564499a320b.zcznbj.com/document/${dataset_id}?ext=${fileExt}&prefix=document`;

        // 更新文件状态为解析中
        const fileIndex = fileList.value.findIndex(item => item.id == file.id);
        if (fileIndex !== -1) {
            fileList.value[fileIndex].status = 'PARSING';

            // 进度从50%逐渐增加到75% (修改为更慢的增长)
            const progressInterval = setInterval(() => {
                if (fileList.value[fileIndex].progress < 75) {
                    // 减慢增长速度，每次只增加0.5%
                    fileList.value[fileIndex].progress += 0.5;
                } else {
                    clearInterval(progressInterval);
                }
            }, 300); // 增加间隔时间，从200ms变为300ms
        }

        // 获取当前用户ID
        const currentUserId = useUserStore().id;
        console.log('当前用户ID:', currentUserId);

        // 更新文件状态和进度 - 结构化处理阶段
        const fileIndex2 = fileList.value.findIndex(item => item.id == file.id);
        if (fileIndex2 !== -1) {
            // 清除之前的计时器
            clearInterval(window['progressInterval_' + file.id]);

            // 进度从75%逐渐增加到98%，预留最后的拼命上传中状态
            window['progressInterval_' + file.id] = setInterval(() => {
                if (fileList.value[fileIndex2].progress < 98) {
                    // 根据当前进度确定增加速度
                    let increment = 0.8; // 默认增长率更快
                    const currentProgress = fileList.value[fileIndex2].progress;

                    // 进度越高，增加越慢
                    if (currentProgress > 80) increment = 0.1; // 80%后显著减慢
                    else if (currentProgress > 50) increment = 0.5; // 50-80%之间中等速度

                    fileList.value[fileIndex2].progress += increment;
                } else {
                    // 到达98%后，切换到拼命上传中状态
                    clearInterval(window['progressInterval_' + file.id]);

                    // 设置为99%并显示拼命上传中状态，添加动画效果
                    fileList.value[fileIndex2].progress = 99;

                    // 创建动画效果，定期更新文本以显示动画点
                    window['dotsAnimation_' + file.id] = setInterval(() => {
                        const dots = '.'.repeat(1 + Math.floor(Date.now() / 500) % 3); // 动态生成1-3个点

                        // 更新文件列表中的显示，强制重新渲染
                        const idx = fileList.value.findIndex(item => item.id === file.id);
                        if (idx !== -1) {
                            // 触发视图更新但不改变进度值
                            fileList.value[idx] = { ...fileList.value[idx] };
                        }
                    }, 500); // 每500毫秒更新一次
                }
            }, 350); // 增加间隔时间，让进度增长更慢
        }

        // 清除所有自动进度定时器
        if (window['autoProgress_' + file.id]) {
            clearInterval(window['autoProgress_' + file.id]);
            window['autoProgress_' + file.id] = null;
        }

        // 清除所有进度计时器
        clearInterval(window['progressInterval_' + file.id]);
        clearInterval(window['dotsAnimation_' + file.id]); // 清除动画点计时器

        // 确保上传完成后不再显示上传中断提示
        const uploadStore = useUploadStore();

        // 手动清理uploadStore中的状态
        const uploadingFileId = uploadStore.uploadingFiles.find(f =>
            f.fileName === file.originalFile.name &&
            Math.abs(f.size - file.originalFile.size) < 10
        )?.id;

        if (uploadingFileId) {
            uploadStore.removeUploadingFile(uploadingFileId);
            console.log('调用addFile接口后，手动从uploadStore中移除文件');
        }

        // 立即检查并更新isUploading状态
        uploadStore.isUploading = uploadStore.uploadingFiles.some(f =>
            f.status === 'UPLOADING' || f.status === 'PARSING'
        );

        // 设置进度为100%并更新状态
        const finalIndex = fileList.value.findIndex(item => item.id == file.id);
        if (finalIndex !== -1) {
            fileList.value[finalIndex].progress = 100;
            fileList.value[finalIndex].status = 'PARSING';
        }

        // 文件上传成功后，直接调用chunksDoc解析接口
        try {
            // 获取docId - 直接使用upFile接口返回的docId
            const docId = response.data && response.data.docId ?
                response.data.docId : responseData.id;

            // 获取datasetId - 从上传响应中获取
            const chunkDatasetId = response.data && response.data.datasetId ?
                response.data.datasetId : responseData.datasetId;

            if (docId && chunkDatasetId) {
                // 调用chunksDoc接口处理文件
                const chunksData = {
                    ids: [docId],
                    datasetId: chunkDatasetId
                };

                console.log('重试上传: 调用chunksDoc接口处理文件，参数:', chunksData);
                const chunksResponse = await chunksDoc(chunksData);
                console.log('重试上传: chunksDoc接口响应:', chunksResponse);

                if (chunksResponse && chunksResponse.code === 0) {
                    console.log('重试上传: 文件chunksDoc处理成功');
                } else {
                    console.warn('重试上传: 文件chunksDoc处理返回异常:', chunksResponse);
                }
            } else {
                console.warn('重试上传: 未找到有效的docId或datasetId，无法调用解析接口');
                console.log('重试上传: docId:', docId, 'chunkDatasetId:', chunkDatasetId);
                console.log('重试上传: response.data:', response.data);
                console.log('重试上传: responseData:', responseData);
            }
        } catch (chunkError) {
            console.error('重试上传: 调用文件解析接口出错:', chunkError);
            // 解析错误不影响上传成功提示
        }

        // 延迟一小段时间后显示成功提示，给用户时间看到100%
        setTimeout(() => {
            showToast('文件重新上传成功');
        }, 500);

        // 文件上传成功后，使用统一的文件加载方法刷新文件列表
        try {
            console.log('文件重新上传成功，准备刷新文件列表');

            // 延时调用，确保DOM更新完成
            setTimeout(async () => {
                // 使用统一的加载方法，传入当前知识库ID
                await loadKnowledgeBaseFiles(repoIds.value);

                console.log('文件列表刷新完成');
            }, 800); // 延长时间确保DOM完全更新
        } catch (listError) {
            console.error('刷新文件列表出错:', listError);
            // 即使刷新出错，也不影响上传成功的提示
        }
    } catch (error) {
        console.error('重试上传文件失败:', error);

        // 更新文件状态为上传失败
        const index = fileList.value.findIndex(item => item.id === file.id);
        if (index !== -1) {
            fileList.value[index].status = 'UPLOAD_ERROR';
            // 确保保留原始文件引用以便再次重试
            if (file.originalFile) {
                fileList.value[index].originalFile = file.originalFile;
            }

            // 清除自动进度递增定时器
            if (window['autoProgress_' + file.id]) {
                clearInterval(window['autoProgress_' + file.id]);
                window['autoProgress_' + file.id] = null;
            }
        }

        showToast('重试上传失败，请检查网络后再试', 'error');
    }
};

// 显示用户权限
const showUserPermissions = () => {
    const userStore = useUserStore();
    const permissions = userStore.permissions || [];
    console.log('用户权限列表:', permissions);

};

// 检查是否有知识库成员管理权限
const hasKnowledgeMemberPermission = computed(() => {
    const userStore = useUserStore();
    const permissions = userStore.permissions || [];
    return permissions.includes('ai:knowledge:member');
});

// 判断用户是否为最高级管理员
const isHighLevelAdmin = computed(() => {
    const userStore = useUserStore();
    const permissions = userStore.permissions || [];
    return permissions.includes('ai:knowledge:member');
});

// 判断用户是否为当前知识库的管理员
const isCurrentRepoAdmin = computed(() => {
    // 如果不是在查看特定知识库，则不是该知识库的管理员
    if (!activeMenuItem.value || !activeMenuItem.value.startsWith('repo_')) {
        return false;
    }

    // 获取当前知识库ID
    const currentRepoId = activeMenuItem.value.split('_')[1];

    // 查找当前知识库
    const currentRepo = repoList.value.find(repo => repo.repoId == currentRepoId);

    // 如果找到知识库且用户在其中的操作权限是EDIT，则用户是该知识库的管理员
    return currentRepo && currentRepo.operationPermission === 'EDIT';
});

// 判断用户是否已加入当前知识库
const isJoinedCurrentRepo = computed(() => {
    // 如果不是在查看特定知识库，则不是该知识库的成员
    if (!activeMenuItem.value || !activeMenuItem.value.startsWith('repo_')) {
        return false;
    }

    // 获取当前知识库ID
    const currentRepoId = activeMenuItem.value.split('_')[1];

    // 查找当前知识库
    const currentRepo = repoList.value.find(repo => repo.repoId == currentRepoId);

    // 如果找到知识库且用户已加入，则用户是该知识库的成员
    return currentRepo && currentRepo.isJoined;
});

// 检查是否应该显示上传文件按钮
const canShowUploadButton = computed(() => {
    // 如果当前是在单位知识库的全部视图，不显示上传按钮
    if (activeMenuGroup.value === 'unit' && activeMenuItem.value === 'all') {
        return false;
    }

    // 如果不是在查看特定知识库，而是在所有知识库页面，不允许上传
    if (!activeMenuItem.value || activeMenuItem.value === 'all' || activeMenuItem.value === 'square') {
        return false;
    }

    // 情况1: 最高级管理员始终可以上传文件
    if (isHighLevelAdmin.value) {
        return true;
    }

    // 情况2: 知识库管理员可以上传文件
    if (isCurrentRepoAdmin.value && isJoinedCurrentRepo.value) {
        return true;
    }

    // 情况3: 普通成员也可以上传文件，但只能操作自己的文件
    if (isJoinedCurrentRepo.value) {
        return true;
    }

    return false;
});

// 在script setup部分添加解析函数
// ... existing code ...
// 解析结构化数据，提取JSON中的内容
const parseStructuredData = (data) => {
    if (!data) return '暂无';

    try {
        // 尝试解析JSON
        const parsedData = JSON.parse(data);

        // 如果是对象，查找常见的内容字段
        if (typeof parsedData === 'object' && parsedData !== null) {
            // 检查常见的内容字段名称
            const contentFields = ['summary', 'content', 'description', 'text', 'message'];

            for (const field of contentFields) {
                if (parsedData[field]) {
                    return parsedData[field];
                }
            }

            // 如果没有找到预定义的字段，返回第一个非空值
            for (const key in parsedData) {
                if (parsedData[key] && typeof parsedData[key] === 'string') {
                    return parsedData[key];
                }
            }

            // 如果没有找到任何有效内容，返回JSON字符串
            return JSON.stringify(parsedData);
        }

        // 如果不是对象，直接返回
        return data;
    } catch (e) {
        // 如果解析失败，返回原始数据
        console.error('解析结构化数据失败:', e);
        return data;
    }
};
// ... existing code ...

// 处理文件选择变化
const handleFileSelectionChange = () => {
    // 检查可选择的文件状态
    const availableFiles = selectableFiles.value;
    if (availableFiles.length === 0) {
        // 没有可选择的文件，不设置全选状态
        selectAllFiles.value = false;
    } else {
        // 计算选中的文件数量
        const selectedCount = availableFiles.filter(file => file.selected).length;

        // 如果所有可选择的文件都被选中，设置为全选状态
        if (selectedCount === availableFiles.length) {
            selectAllFiles.value = true;
        }
        // 如果部分文件被选中，设置为null（半选中状态）
        else if (selectedCount > 0) {
            selectAllFiles.value = null;
        }
        // 如果没有文件被选中，取消全选状态
        else {
            selectAllFiles.value = false;
        }
    }
};

// 在script setup部分添加跳转函数
// ... existing code ...
import { useRouter } from 'vue-router';

// 获取路由实例
const router = useRouter();

// 添加查看PDF文件的方法
const viewPdf = (file, isEdit = false) => {
    if (!file || !file.fileId) {
        showToast('文件ID不存在，无法查看', 'error');
        return;
    }

    // 保存当前知识库状态，以便返回时恢复
    saveKnowledgeBaseState();

    // 使用文件的fileMd5作为remark参数
    const remark = file.fileMd5 || file.md5 || null;

    // 路由跳转到PDF预览页面，并传递fileId、是否编辑以及管理员权限参数
    router.push({
        path: '/pdf-preview',
        query: {
            fileId: file.fileId,
            isEdit: isEdit ? '1' : '0',
            isHighLevelAdmin: isHighLevelAdmin.value ? '1' : '0', // 传递是否是最高级管理员参数
            isAdmin: isCurrentRepoAdmin.value ? '1' : '0', // 传递是否是普通管理员参数
            remark: remark // 传递知识库的remark或单位知识库的publicBucketName
        }
    });
};

// 当前查看的文件信息
const currentViewingFile = ref(null);
const showFileViewer = ref(false);
const fileViewerUrl = ref('');
const fileViewerType = ref('');
const jsPdf = ref(''); // 添加jsPdf变量用于PDF预览

// 检测文件类型
const detectFileType = (filename) => {
    const extension = filename.split('.').pop().toLowerCase();

    const types = {
        // 图片
        jpg: 'image', jpeg: 'image', png: 'image', gif: 'image', bmp: 'image', webp: 'image',
        // 文档
        pdf: 'pdf',
        doc: 'office', docx: 'office', xls: 'office', xlsx: 'office', ppt: 'office', pptx: 'office',
        // 文本
        txt: 'text', csv: 'text', json: 'text', xml: 'text',
        // 压缩文件
        zip: 'archive', rar: 'archive', '7z': 'archive', tar: 'archive', gz: 'archive',
        // 音视频
        mp3: 'audio', wav: 'audio', ogg: 'audio',
        mp4: 'video', webm: 'video', mov: 'video', avi: 'video',
        docx: 'docx', doc: 'docx',
        xlsx: 'xlsx', xls: 'xlsx',
        pptx: 'ppt', ppt: 'ppt',
    };

    return {
        extension,
        type: types[extension] || 'unknown'
    };
};

// 在当前页面查看文件
const viewFileInline = async (file) => {
    // 处理参考文档类型，它可能带有doc_id而不是fileId
    const fileId = file.fileId || file.doc_id;
    const fileName = file.fileName || file.doc_name || '未命名文件';

    if (!fileId) {
        showToast('文件ID不存在，无法预览', 'error');
        return;
    }

    // 保存当前知识库状态，以便在弹窗关闭后保持状态
    saveKnowledgeBaseState();

    // 使用文件的fileMd5作为remark参数
    const remark = file.fileMd5 || file.md5 || null;

    try {
        // 设置当前查看的文件，并添加remark信息
        currentViewingFile.value = {
            ...file,
            fileId: fileId,
            fileName: fileName,
            remark: remark // 添加remark信息
        };

        // 使用downloadFile2 API获取文件数据
        const response = await downloadFile2(fileId);

        if (response) {
            // 检查返回的数据是否为有效的blob数据
            const isValidBlob = blobValidate(response);

            if (!isValidBlob) {
                // 如果不是有效的blob数据，说明返回的是错误信息（JSON格式）
                try {
                    const errorText = await response.text();
                    const errorData = JSON.parse(errorText);

                    // 检查是否是code == 500的错误
                    if (errorData.code == 500) {
                        showToast('文件不存在或已损坏', 'error');
                        return;
                    } else {
                        // 其他错误情况
                        showToast(errorData.msg || '获取文件数据失败', 'error');
                        return;
                    }
                } catch (parseError) {
                    console.error('解析错误响应失败:', parseError);
                    showToast('获取文件数据失败', 'error');
                    return;
                }
            }

            // 检测文件类型
            const { type, extension } = detectFileType(fileName);

            let blob = '';
            if (type === 'pdf') {
                // 对PDF文件特殊处理
                blob = new Blob([response], { type: 'application/pdf' });
                // 创建Blob URL用于PDFView组件
                jsPdf.value = URL.createObjectURL(blob);
                console.log('PDF Blob URL创建成功:', jsPdf.value);
            } else if (type === 'image') {
                blob = new Blob([response], { type: `image/${extension}` });
            } else if (type === 'text') {
                blob = await response.text();
            } else {
                // 其他类型文件
                blob = new Blob([response]);
            }

            // 设置文件信息
            if (type === 'text') {
                fileInfo.value = {
                    'filePath': blob,
                    'fileType': type
                };
            } else if (type === 'pdf') {
                // PDF文件使用jsPdf变量，不需要设置fileInfo
                fileInfo.value = {
                    'filePath': '',
                    'fileType': type
                };
            } else {
                fileInfo.value = {
                    'filePath': URL.createObjectURL(blob),
                    'fileType': type
                };
            }

            // 显示查看器
            showFileViewer.value = true;

            console.log('文件查看器已打开');
            if (type === 'pdf') {
                console.log('使用PDFView组件预览PDF文件');
            } else {
                console.log('使用officePreview组件预览');
            }
            console.log('文件类型:', type);
        } else {
            showToast('获取文件数据失败', 'error');
        }
    } catch (error) {
        console.error('预览文件失败:', error);

        // 检查错误是否包含特定的状态码信息
        if (error.message && error.message.includes('500')) {
            showToast('文件不存在或已损坏', 'error');
        } else {
            showToast('预览文件失败，请重试', 'error');
        }
    }
};

// 根据文件扩展名获取MIME类型
const getMimeTypeFromExtension = (extension) => {
    const mimeTypes = {
        'pdf': 'application/pdf',
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls': 'application/vnd.ms-excel',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'ppt': 'application/vnd.ms-powerpoint',
        'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'svg': 'image/svg+xml',
        'mp3': 'audio/mpeg',
        'wav': 'audio/wav',
        'mp4': 'video/mp4',
        'avi': 'video/x-msvideo',
        'mov': 'video/quicktime',
        'txt': 'text/plain',
        'html': 'text/html',
        'css': 'text/css',
        'js': 'text/javascript',
        'json': 'application/json',
        'xml': 'application/xml',
        'zip': 'application/zip',
        'rar': 'application/x-rar-compressed',
        '7z': 'application/x-7z-compressed'
    };

    return mimeTypes[extension] || null;
};

// 关闭文件查看器
const closeFileViewer = () => {
    // 如果fileInfo中的filePath是Blob URL，需要释放它
    if (fileInfo.value && fileInfo.value.filePath && typeof fileInfo.value.filePath === 'string' && fileInfo.value.filePath.startsWith('blob:')) {
        URL.revokeObjectURL(fileInfo.value.filePath);
    }

    // 释放PDF的Blob URL
    if (jsPdf.value) {
        URL.revokeObjectURL(jsPdf.value);
        jsPdf.value = '';
    }

    showFileViewer.value = false;
    fileViewerUrl.value = '';
    currentViewingFile.value = null;
    fileInfo.value = {};
};

// 添加打印文件数据的方法 (保留但修改为调用viewFileInline)
const logFileData = (file) => {
    // 直接调用内联查看
    viewFileInline(file);

    // 以下是原有的调试信息
    let Apiurl = getApiBaseUrl() + file.filePath
    console.log('Apiurl:', Apiurl);
    console.log('========== 文件数据开始 ==========');
    console.log('完整文件对象:', file);
    console.log('文件ID (fileId):', file.fileId);
    console.log('文件名称 (fileName):', file.fileName);
    console.log('文件路径 (filePath):', file.filePath);
    console.log('文件大小 (fileSize):', file.fileSize);
    console.log('文件类型 (fileType):', file.fileType);
    console.log('文件状态 (status):', file.status);
    console.log('创建者 (createBy):', file.createBy);
    console.log('创建时间 (createTime):', file.createTime);
    console.log('权限类型 (permissionType):', file.permissionType);
    console.log('操作权限 (operationPermission):', file.operationPermission);
    console.log('结构化数据 (structuredData):', file.structuredData);
};

// 添加查看原始文件的方法
const viewOriginalFile = async (file) => {
    if (!file || !file.fileId) {
        showToast('文件ID不存在，无法查看', 'error');
        return;
    }

    try {
        // 使用getDownloadUrl API获取临时URL
        const tempUrl = await getDownloadUrl(file.fileId);

        if (tempUrl) {
            // 在新窗口打开URL预览文件
            window.open(tempUrl, '_blank');
        } else {
            showToast('获取文件预览链接失败', 'error');
        }
    } catch (error) {
        console.error('预览文件失败:', error);
        showToast('预览文件失败，请重试', 'error');
    }
};

// 下载文件
const downloadFile = async (file) => {
    try {
        if (!file || !file.fileId) {
            showToast('文件ID不存在，无法下载', 'error');
            return;
        }

        // 使用axios直接下载文件，通过Blob方式处理
        try {
            // 显示加载提示
            const loadingInstance = ElLoading.service({
                text: "正在下载文件，请稍候",
                background: "rgba(0, 0, 0, 0.7)"
            });

            // 使用axios直接下载
            const response = await axios({
                url: `${baseURL}app/api/system/filesApi/download/${file.fileId}`,
                method: 'GET',
                responseType: 'blob',
                headers: {
                    'Authorization': 'Bearer ' + getToken()
                }
            });

            // 关闭加载提示
            loadingInstance.close();

            // 获取文件名
            const contentDisposition = response.headers['content-disposition']
            // 默认文件名
            let fileName = file.fileName || '下载文件'
            let fileExtension = ''

            // 从响应头中提取文件名
            if (contentDisposition) {
                const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
                if (fileNameMatch && fileNameMatch[1]) {
                    const extractedName = decodeURIComponent(fileNameMatch[1].replace(/['"]/g, ''))
                    if (extractedName) {
                        fileName = extractedName
                    }
                }
            }

            // 从文件名或Content-Type中获取文件扩展名
            const fileNameParts = fileName.split('.')
            if (fileNameParts.length > 1) {
                fileExtension = fileNameParts.pop().toLowerCase()
            } else {
                // 如果文件名中没有扩展名，从Content-Type中获取
                const contentType = response.headers['content-type']
                if (contentType) {
                    // 根据常见MIME类型映射到对应的文件扩展名
                    if (contentType.includes('application/pdf')) {
                        fileExtension = 'pdf'
                    } else if (contentType.includes('image/jpeg')) {
                        fileExtension = 'jpg'
                    } else if (contentType.includes('image/png')) {
                        fileExtension = 'png'
                    } else if (contentType.includes('text/plain')) {
                        fileExtension = 'txt'
                    } else if (contentType.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document')) {
                        fileExtension = 'docx'
                    } else if (contentType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')) {
                        fileExtension = 'xlsx'
                    } else if (contentType.includes('application/vnd.openxmlformats-officedocument.presentationml.presentation')) {
                        fileExtension = 'pptx'
                    }

                    // 如果从Content-Type中确定了扩展名，添加到文件名中
                    if (fileExtension && !fileName.endsWith(`.${fileExtension}`)) {
                        fileName = `${fileName}.${fileExtension}`
                    }
                }
            }

            // 创建下载
            const blob = new Blob([response.data], { type: response.headers['content-type'] })
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(link.href)

            ElMessage.success('下载成功')
            return true

        } catch (error) {
            console.error('使用axios下载文件失败:', error);
            showToast('下载文件失败，请重试', 'error');
        }
    } catch (error) {
        console.error('下载文件失败:', error);
        // showToast('下载文件失败，请重试', 'error');
    }
};



// 下载选中的文件
const downloadSelectedFiles = async () => {
    // 选择以下文件类型:
    // 1. 已成功解析的文件
    // 2. 自己上传的解析中文件
    // 3. 解析失败的文件(最高级管理员、知识库管理员或自己上传的)
    const selectedFiles = fileList.value.filter(file =>
        file.selected && (
            file.status === 'PARSE_SUCCESS' ||
            (file.status === 'PARSING' && (isHighLevelAdmin.value || (isCurrentRepoAdmin.value && isJoinedCurrentRepo.value) || file.userId === userStore.id)) ||
            (file.status === 'PARSE_FAILED' && (
                isHighLevelAdmin.value ||
                (isCurrentRepoAdmin.value && isJoinedCurrentRepo.value) ||
                file.userId === userStore.id
            ))
        )
    );

    if (selectedFiles.length === 0) {
        showToast('请先选择要下载的文件', 'error');
        return;
    }

    // 显示正在处理的提示
    showToast(`开始下载 ${selectedFiles.length} 个文件`);

    // 按顺序下载文件，避免同时触发太多下载
    for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];

        try {
            // 等待一小段时间，避免浏览器拦截多个下载
            if (i > 0) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // 调用单文件下载函数
            await downloadFile(file);
        } catch (error) {
            console.error(`下载文件 ${file.fileName} 失败:`, error);
        }
    }
};

const shareFile = async (file) => {
    console.log('分享操作 - 文件对象:', file);
    // 添加更多调试信息
    console.log('当前用户ID:', userStore.id);
    console.log('当前用户名:', userStore.name);
    console.log('文件创建者:', file.createBy);
    console.log('文件权限类型:', file.permissionType);
    console.log('文件用户ID:', file.userId);

    // 检查用户权限
    const isCreator = file.userId == userStore.id;
    const hasSharePermission = isHighLevelAdmin.value || (isCurrentRepoAdmin.value && isJoinedCurrentRepo.value) || isCreator;
    console.log('是否为创建者:', isCreator);
    console.log('是否有共享权限:', hasSharePermission);

    // 只允许有权限的用户共享/取消共享
    if (!hasSharePermission) {
        showToast('您没有更改此文件共享状态的权限', 'error');
        return;
    }

    // 根据当前文件共享状态确定操作类型和新的权限类型
    const currentPermissionType = file.permissionType;
    console.log('当前权限类型:', currentPermissionType);

    let newPermissionType = '';
    let confirmMessage = '';
    let successMessage = '';

    // 根据当前权限确定新的权限和提示消息
    if (currentPermissionType === 'ALL') {
        // 从公共变为部门私有
        newPermissionType = 'REPO';
        confirmMessage = '确定要取消共享此文件吗？取消后其他用户将无法查看此文件。';
        successMessage = '已取消共享';
    } else {
        // 从部门私有变为公共
        newPermissionType = 'ALL';
        confirmMessage = '确定要将此文件设为共享吗？设置后所有用户都可以查看此文件。';
        successMessage = '已设为共享';
    }

    // 弹出确认对话框
    if (!confirm(confirmMessage)) {
        return; // 用户取消操作
    }

    // 如果当前是在单位知识库，并且要取消共享，提示用户
    if (activeMenuGroup.value === 'unit' && currentPermissionType === 'ALL') {
        showToast('单位知识库中的文件可以取消共享，但无法再次分享', 'info');
    }

    try {
        // 使用docMove接口而不是editFile
        // 确定datasetId: 当前点击的知识库的remark，单位知识库就传userStore.publicBucketName
        let datasetId = userStore.publicBucketName; // 默认使用单位知识库
        if (repoIds.value) {
            // 如果有选择知识库，查找对应的知识库信息
            const selectedRepo = repoList.value.find(repo => repo.repoId == repoIds.value);
            if (selectedRepo && selectedRepo.remark) {
                datasetId = selectedRepo.remark;
            }
        }

        // 准备请求参数
        // 按照需求，当前是ALL时要传REPO，反之亦然
        const targetPermissionType = currentPermissionType === 'ALL' ? 'REPO' : 'ALL';

        const params = {
            datasetId: datasetId,
            docId: file.fileId,
            permissionType: targetPermissionType, // 如果当前是ALL就传REPO，反之就是REPO情况下传ALL
            repoId: file.repoId,
            fileName: file.fileName
        };

        console.log('调用docMove API:', params);
        await docMove(params);
        showToast(successMessage);

        // 刷新文件列表
        console.log('准备刷新文件列表');

        // 不仅刷新当前知识库的文件列表，确保UI状态正确
        await loadKnowledgeBaseFiles(repoIds.value);

        console.log('文件列表刷新完成');
    } catch (e) {
        console.error('分享操作失败:', e);
        showToast('操作失败', 'error');
    }
};

const batchShareFiles = async (permissionType) => {
    // 根据操作类型筛选文件，只处理自己上传的文件
    let selectedFiles = [];

    if (permissionType === 'REPO') {
        // 取消共享操作 - 只选择解析成功的自己上传的已共享文件
        selectedFiles = fileList.value.filter(file =>
            file.selected &&
            file.status === 'PARSE_SUCCESS' &&
            file.permissionType === 'ALL' &&
            file.userId == userStore.id
        );
    } else {
        // 共享操作 - 只选择解析成功的自己上传的私有文件
        // 如果是在单位知识库，不允许批量分享操作
        if (activeMenuGroup.value === 'unit') {
            showToast('单位知识库中的文件无法再次分享', 'info');
            return;
        }

        selectedFiles = fileList.value.filter(file =>
            file.selected &&
            file.status === 'PARSE_SUCCESS' &&
            file.permissionType === 'REPO' &&
            file.userId == userStore.id
        );
    }

    if (selectedFiles.length === 0) {
        showToast('没有可以操作的文件', 'error');
        return;
    }

    // 准备确认信息
    const confirmMessage = permissionType === 'REPO'
        ? `确定要取消共享选中的 ${selectedFiles.length} 个文件吗？取消后其他用户将无法查看这些文件。`
        : `确定要将选中的 ${selectedFiles.length} 个文件设为共享吗？设置后所有用户都可以查看这些文件。`;

    // 显示确认对话框
    if (!confirm(confirmMessage)) {
        return; // 用户取消操作
    }

    const successMessage = permissionType === 'REPO' ? '批量取消共享成功' : '批量设为共享成功';

    try {
        // 确定datasetId: 当前点击的知识库的remark，单位知识库就传userStore.publicBucketName
        let datasetId = userStore.publicBucketName; // 默认使用单位知识库
        if (repoIds.value) {
            // 如果有选择知识库，查找对应的知识库信息
            const selectedRepo = repoList.value.find(repo => repo.repoId == repoIds.value);
            if (selectedRepo && selectedRepo.remark) {
                datasetId = selectedRepo.remark;
            }
        }

        // 使用docMove接口批量处理文件
        await Promise.all(selectedFiles.map(file => {
            // 根据当前文件的权限类型确定目标权限类型
            // 如果当前操作是取消共享(REPO)，说明文件当前是共享状态(ALL)，所以要传REPO
            // 如果当前操作是共享(ALL)，说明文件当前是私有状态(REPO)，所以要传ALL
            const targetPermissionType = file.permissionType === 'ALL' ? 'REPO' : 'ALL';

            const params = {
                datasetId: datasetId,
                docId: file.fileId,
                permissionType: targetPermissionType, // 根据当前文件状态决定目标权限类型
                repoId: file.repoId,
                fileName: file.fileName
            };

            console.log('批量调用docMove API:', params);
            return docMove(params);
        }));

        showToast(successMessage);

        // 刷新文件列表
        await loadKnowledgeBaseFiles(repoIds.value);
    } catch (e) {
        console.error('批量操作失败:', e);
        showToast('批量操作失败', 'error');
    }
};

// 存储解密后的API凭证
const decryptedCredentials = ref({
    apiKey: '',
    apiUrl: ''
});

// 获取并解密apiKey和apiUrl
const decryptApiCredentials = async () => {
    console.log('=========RSA公钥解密API凭证=========');
    const apiKey = userStore.apiKey;
    const apiUrl = userStore.apiUrl;
    let publicKey = userStore.publicKey

    console.log('加密的apiKey:', apiKey);
    console.log('加密的apiUrl:', apiUrl);
    console.log('当前publicKey:', publicKey);

    try {
        const result = decrypt(apiKey)
        const result1 = decrypt(apiUrl)
        console.log('apiKey解密結果:', result);
        console.log('apiUrl解密結果:', result1);

        // 将解密后的结果保存到decryptedCredentials
        decryptedCredentials.value.apiKey = result;
        decryptedCredentials.value.apiUrl = result1;

        // 将解密后的结果保存到userStore
        userStore.setDecryptedApiKey(result);
        userStore.setDecryptedApiUrl(result1);

        return true;
    } catch (error) {
        console.error('解密失败:', error);
        return false;
    }
};

// 使用解密后的凭证进行操作
const useDecryptedCredentials = () => {
    console.log('使用RSA解密后的API凭证进行操作');
    // 优先使用userStore中的解密凭证，如果没有则使用本地的
    const apiKey = userStore.decryptedApiKey || decryptedCredentials.value.apiKey;
    const apiUrl = userStore.decryptedApiUrl || decryptedCredentials.value.apiUrl;

    if (!apiKey || !apiUrl) {
        console.error('API凭证不完整，无法使用');
        return false;
    }

    try {
        // 使用解密后的apiKey和apiUrl进行操作
        console.log('API凭证已准备就绪:', {
            apiKey: apiKey ? `${apiKey.substring(0, 3)}...${apiKey.substring(apiKey.length - 3)}` : null,
            apiUrl: apiUrl || null
        });

        // 设置全局API配置
        axios.defaults.headers.common['Authorization'] = `Bearer ${apiKey}`;
        axios.defaults.baseURL = apiUrl;

        // 测试API连接
        testApiConnection();

        console.log('全局API配置已设置');
        return true;
    } catch (error) {
        console.error('设置API凭证时出错:', error);
        return false;
    }
};

// 测试API连接
const testApiConnection = async () => {
    try {
        console.log('测试API连接...');
        // 发送一个简单的请求测试连接
        setTimeout(async () => {
            try {
                // 使用设置好的axios默认配置发送请求
                const response = await axios.get('/health', { timeout: 5000 });
                console.log('API连接测试结果:', response.status === 200 ? '成功' : '失败');
            } catch (error) {
                console.log('API连接测试失败，但这可能是因为/health端点不存在，不影响正常使用');
            }
        }, 1000);
    } catch (error) {
        console.error('测试API连接时出错:', error);
    }
};

// 此onMounted已合并到前面的主onMounted钩子中
</script>

<style scoped>
@import '@/styles/home-view.css'
</style>